"""
Multi-drop test suite for Tetris Engine.

This module provides unit testing for multi-piece scenarios including:
- Single piece placement at all valid positions
- Boundary condition testing
- Piece stacking combinations
- Line clearing patterns

These tests focus on core functionality validation and are designed to run
quickly as part of the standard test suite. For comprehensive sequence testing
with extensive combinations, see the async test suite in scripts/.
"""

import pytest

from tetris_engine.constants import SINGLE_PIECE_HEIGHTS, TETROMINO, WIDTH
from tetris_engine.core import Board
from tetris_engine.io import process_file


class TetrisEngineUnitTestMultiDropRunner:
    """Manages unit testing for multi-drop scenarios."""

    def __init__(self):
        self.tetromino_types = list(TETROMINO.keys())
        self.valid_positions = self._compute_valid_positions()

    def _compute_valid_positions(self) -> dict:
        """Compute valid column positions for each piece type."""
        valid_pos = {}
        for tetromino_type in self.tetromino_types:
            footprint = TETROMINO[tetromino_type]
            max_dx = max(dx for dx, _ in footprint)
            # Valid positions are 0 to (WIDTH - 1 - max_dx)
            valid_pos[tetromino_type] = list(range(WIDTH - max_dx))
        return valid_pos


def test_single_piece_all_positions():
    """Test every piece at every valid position with exact height validation."""
    runner = TetrisEngineUnitTestMultiDropRunner()

    for tetromino_type in runner.tetromino_types:
        expected_height = SINGLE_PIECE_HEIGHTS[tetromino_type]

        for col in runner.valid_positions[tetromino_type]:
            board = Board()
            try:
                height = board.drop(tetromino_type, col)

                # Verify exact height for single piece
                assert height == expected_height, (
                    f"Expected height {expected_height} for {tetromino_type} at {col}, "
                    f"got {height}"
                )

                # Verify piece is placed correctly
                footprint = TETROMINO[tetromino_type]
                expected_cells = {(col + dx, dy) for dx, dy in footprint}
                assert expected_cells.issubset(
                    board.occupied
                ), f"Piece {tetromino_type} at {col} not placed correctly"

            except Exception as e:
                pytest.fail(f"Failed to drop {tetromino_type} at column {col}: {e}")


def test_boundary_conditions():
    """Test all boundary conditions for each piece."""
    runner = TetrisEngineUnitTestMultiDropRunner()

    for tetromino_type in runner.tetromino_types:
        board = Board()

        # Test leftmost valid position
        min_col = min(runner.valid_positions[tetromino_type])
        height = board.drop(tetromino_type, min_col)
        assert height > 0

        # Test rightmost valid position
        max_col = max(runner.valid_positions[tetromino_type])
        board = Board()
        height = board.drop(tetromino_type, max_col)
        assert height > 0

        # Test invalid positions (should raise ValueError)
        if min_col > 0:
            board = Board()
            with pytest.raises(ValueError):
                board.drop(tetromino_type, min_col - 1)

        if max_col < WIDTH - 1:
            board = Board()
            with pytest.raises(ValueError):
                board.drop(tetromino_type, max_col + 1)


def test_stacking_all_combinations():
    """Test stacking every piece on every other piece."""
    runner = TetrisEngineUnitTestMultiDropRunner()

    for tetromino_type1 in runner.tetromino_types:
        for col1 in runner.valid_positions[tetromino_type1]:
            for tetromino_type2 in runner.tetromino_types:
                for col2 in runner.valid_positions[tetromino_type2]:
                    board = Board()

                    # Drop first piece
                    h1 = board.drop(tetromino_type1, col1)

                    # Drop second piece
                    h2 = board.drop(tetromino_type2, col2)

                    # Height should be non-decreasing
                    assert h2 >= h1, (
                        f"Height decreased: {tetromino_type1}@{col1} -> "
                        f"{tetromino_type2}@{col2}"
                    )


def test_line_clearing_patterns():
    """Test all possible line clearing scenarios."""
    # Test single row clear with Q pieces
    board = Board()
    for col in [0, 2, 4, 6, 8]:  # Fill row with Q pieces
        board.drop("Q", col)
    assert board.drop("I", 0) == 1  # Should clear and leave height 1

    # Test multiple row clear
    board = Board()
    # Create a pattern that clears multiple rows
    for _ in range(2):
        for col in [0, 2, 4, 6, 8]:
            board.drop("Q", col)
    height = board.drop("I", 0)
    assert height == 1

    # Test partial line (no clear)
    board = Board()
    for col in [0, 2, 4, 6]:  # Only 4 Q pieces, not enough to clear
        board.drop("Q", col)
    height = board.drop("I", 0)  # Drop I piece at valid position
    # Height depends on how the I piece stacks with the Q pieces
    assert height >= 2, f"Height should be at least 2, got {height}"


def test_io_basic_patterns(tmp_path):
    """Test I/O processing with basic input patterns."""
    runner = TetrisEngineUnitTestMultiDropRunner()

    # Test all single piece inputs
    for tetromino_type in runner.tetromino_types:
        for col in runner.valid_positions[tetromino_type]:
            input_file = tmp_path / f"test_{tetromino_type}_{col}.txt"
            output_file = tmp_path / f"out_{tetromino_type}_{col}.txt"

            input_file.write_text(f"{tetromino_type}{col}\n")
            process_file(str(input_file), str(output_file))

            result = output_file.read_text().strip()
            assert (
                result.isdigit()
            ), f"Invalid output for {tetromino_type}{col}: {result}"

            expected_height = SINGLE_PIECE_HEIGHTS[tetromino_type]
            assert (
                int(result) == expected_height
            ), f"Expected {expected_height} for {tetromino_type}{col}, got {result}"


if __name__ == "__main__":
    # Run multi-drop tests when called directly
    print("Starting multi-drop test suite...")

    runner = TetrisEngineUnitTestMultiDropRunner()
    print(
        f"Testing {len(runner.tetromino_types)} pieces with "
        f"{sum(len(positions) for positions in runner.valid_positions.values())} "
        f"total valid positions"
    )

    # Run tests
    test_single_piece_all_positions()
    print("✓ Single piece tests completed")

    test_boundary_conditions()
    print("✓ Boundary condition tests completed")

    test_stacking_all_combinations()
    print("✓ Stacking combination tests completed")

    test_line_clearing_patterns()
    print("✓ Line clearing tests completed")

    print("Multi-drop test suite completed successfully")
