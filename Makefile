.PHONY: install lint lint-fix test test-exhaustive test-parallel clean docs all

install:
	python3 -m venv .venv_tetris && . .venv_tetris/bin/activate && \
	pip install --upgrade pip && \
	pip install -e .[dev]

lint:
	. .venv_tetris/bin/activate && flake8 src tests --extend-ignore=E501,W503 --max-line-length=88
	. .venv_tetris/bin/activate && mypy src tests

lint-fix:
	. .venv_tetris/bin/activate && autopep8 --in-place --recursive --aggressive --aggressive src tests
	. .venv_tetris/bin/activate && isort src tests
	. .venv_tetris/bin/activate && black src tests
	. .venv_tetris/bin/activate && flake8 src tests --extend-ignore=E501,W503 --max-line-length=88
	. .venv_tetris/bin/activate && mypy src tests

test:
	. .venv_tetris/bin/activate && pytest --cov=src --cov-report=term

test-parallel:
	. .venv_tetris/bin/activate && pytest -n auto --cov=src --cov-report=term

test-exhaustive:
	. .venv_tetris/bin/activate && python scripts/gen_and_run_test_cases.py --max-minutes 120.0

test-async-quick:
	. .venv_tetris/bin/activate && python scripts/gen_and_run_test_cases.py --max-minutes 30.0

docs:
	. .venv_tetris/bin/activate && cd docs/_sphinx && make html

clean:
	rm -rf .venv_tetris
	rm -rf src/*.egg-info
	rm -rf build/
	rm -rf dist/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf docs/_sphinx/_build/
	rm -rf exhaustive_test_results.json
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

all: install lint test docs
