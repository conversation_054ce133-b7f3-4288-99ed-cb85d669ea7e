#!/usr/bin/env python3
"""
Monitor script for async smoke test cases.
Tracks progress and provides estimates for the async smoke test runner.
"""

import time
import os
import re
from datetime import datetime

def parse_log_file(log_file):
    """Parse the log file to extract progress information."""
    if not os.path.exists(log_file):
        return None
    
    with open(log_file, 'r') as f:
        content = f.read()
    
    # Extract current sequence length being tested
    length_matches = re.findall(r'Testing sequences of length (\d+)', content)
    current_length = int(length_matches[-1]) if length_matches else 1
    
    # Extract total combinations for current length
    combo_matches = re.findall(r'Generating ALL (\d+) combinations for length (\d+)', content)
    current_total = 0
    if combo_matches:
        for total, length in combo_matches:
            if int(length) == current_length:
                current_total = int(total)
                break
    
    # Extract completed tests for current length
    completed_matches = re.findall(r'Completed (\d+)/' + str(current_total) + r' tests', content)
    current_completed = int(completed_matches[-1]) if completed_matches else 0
    
    # Extract overall test counts
    summary_match = re.search(r'Total tests: (\d+)', content)
    total_tests_so_far = int(summary_match.group(1)) if summary_match else 0
    
    return {
        'current_length': current_length,
        'current_total': current_total,
        'current_completed': current_completed,
        'total_tests_so_far': total_tests_so_far
    }

def estimate_remaining_time(progress_info, start_time):
    """Estimate remaining time based on current progress."""
    if not progress_info or progress_info['current_completed'] == 0:
        return "Unknown"
    
    elapsed = time.time() - start_time
    
    # Calculate rate for current length
    current_rate = progress_info['current_completed'] / elapsed  # tests per second
    
    if current_rate == 0:
        return "Unknown"
    
    # Estimate time to complete current length
    remaining_current = progress_info['current_total'] - progress_info['current_completed']
    time_for_current = remaining_current / current_rate
    
    # Very rough estimate for remaining lengths (this is highly speculative)
    # Each subsequent length has 58x more combinations
    current_length = progress_info['current_length']
    remaining_lengths = max(0, min(10, 200 - current_length))  # Cap at 10 more lengths for sanity
    
    # Exponential growth estimate (very rough)
    time_for_remaining = 0
    if remaining_lengths > 0:
        base_combinations = progress_info['current_total']
        for i in range(1, remaining_lengths + 1):
            next_combinations = base_combinations * 58  # 58 valid positions
            time_for_length = next_combinations / current_rate
            time_for_remaining += time_for_length
            base_combinations = next_combinations
            
            # Cap the estimate to avoid ridiculous numbers
            if time_for_remaining > 3600 * 24:  # More than 24 hours
                time_for_remaining = 3600 * 24
                break
    
    total_remaining = time_for_current + time_for_remaining
    return total_remaining

def format_time(seconds):
    """Format seconds into human readable time."""
    if seconds == "Unknown":
        return "Unknown"
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if hours > 0:
        return f"{hours}h {minutes}m {secs}s"
    elif minutes > 0:
        return f"{minutes}m {secs}s"
    else:
        return f"{secs}s"

def main():
    """Main monitoring loop."""
    log_file = "async_smoke_test_output.log"
    start_time = time.time()

    print("Async Smoke Test Monitor")
    print("=" * 50)
    
    while True:
        try:
            progress = parse_log_file(log_file)
            
            if progress:
                elapsed = time.time() - start_time
                remaining = estimate_remaining_time(progress, start_time)
                
                print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Progress Update:")
                print(f"  Current Length: {progress['current_length']}")
                print(f"  Current Progress: {progress['current_completed']:,}/{progress['current_total']:,} "
                      f"({progress['current_completed']/max(1,progress['current_total'])*100:.1f}%)")
                print(f"  Total Tests So Far: {progress['total_tests_so_far']:,}")
                print(f"  Elapsed Time: {format_time(elapsed)}")
                print(f"  Estimated Remaining: {format_time(remaining)}")
                
                # Check if process is still running
                import subprocess
                result = subprocess.run(['pgrep', '-f', 'sample_async_smoke_test.py'],
                                      capture_output=True, text=True)
                if not result.stdout.strip():
                    print("\n  Status: Process completed or stopped")
                    break
                else:
                    print(f"  Status: Running (PID: {result.stdout.strip()})")
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] Waiting for log file...")
            
            time.sleep(30)  # Update every 30 seconds
            
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")
            break
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(10)

if __name__ == "__main__":
    main()
