#!/usr/bin/env python3
"""
Async smoke test generator and runner for Tetris Engine.

This script is designed for comprehensive async smoke testing scenarios where
extensive sequence testing is performed asynchronously. It generates test cases
with fixed random seeds for reproducible results and executes them using
multiprocessing for efficiency.

Key features:
- Fixed seed for reproducible test generation
- Parallel execution on sequences of increasing length
- Checkpointing for long-running tests
- Designed for CI/CD deployment to detect output changes

This is intended for extensive validation and correctness testing by comparing
results to previous versions using the same fixed input (random seed).
"""

import itertools
import multiprocessing as mp
import time
import sys
import os
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Tuple, Dict
import json
import random

from tetris_engine.core import Board
from tetris_engine.constants import TETROMINO, WIDTH, SEED


class TetrisEngineAsyncTestRunner:
    """Comprehensive async test runner for sequence generation and execution."""

    def __init__(self, max_sequence_length: int = 10):
        self.tetromino_types = list(TETROMINO.keys())
        self.max_sequence_length = max_sequence_length
        self.valid_positions = self._compute_valid_positions()
        self.results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "errors": [],
        }

    def _compute_valid_positions(self) -> Dict[str, List[int]]:
        """Compute valid column positions for each piece type."""
        valid_pos = {}
        for tetromino_type in self.tetromino_types:
            footprint = TETROMINO[tetromino_type]
            max_dx = max(dx for dx, _ in footprint)
            valid_pos[tetromino_type] = list(range(WIDTH - max_dx))
        return valid_pos

    def generate_all_single_drops(self) -> List[Tuple[str, int]]:
        """Generate all possible single piece drops."""
        drops = []
        for tetromino_type in self.tetromino_types:
            for col in self.valid_positions[tetromino_type]:
                drops.append((tetromino_type, col))
        return drops

    def generate_sequence_combinations(
        self, length: int, sample_size: int = None
    ) -> List[List[Tuple[str, int]]]:
        """Generate sequence combinations with fixed seed for reproducibility."""
        single_drops = self.generate_all_single_drops()

        if length == 0:
            return [[]]
        elif length == 1:
            return [[drop] for drop in single_drops]
        else:
            # Use fixed seed for reproducible generation
            random.seed(SEED)
            
            total_combinations = len(single_drops) ** length
            sequences = []
            
            if sample_size is not None and total_combinations > sample_size:
                # Sample with fixed seed for reproducibility
                print(
                    f"Sampling {sample_size} from {total_combinations} total "
                    f"combinations for length {length} (seed={SEED})"
                )
                for _ in range(sample_size):
                    sequence = []
                    for _ in range(length):
                        sequence.append(random.choice(single_drops))
                    sequences.append(sequence)
            else:
                # Generate ALL combinations
                print(
                    f"Generating ALL {total_combinations} combinations for length {length}"
                )
                for combo in itertools.product(single_drops, repeat=length):
                    sequences.append(list(combo))

            return sequences

    def test_single_sequence(self, sequence: List[Tuple[str, int]]) -> Dict:
        """Test a single sequence and return results."""
        try:
            board = Board()
            heights = []

            for i, (piece, col) in enumerate(sequence):
                height = board.drop(piece, col)
                heights.append(height)

                # Validate height is reasonable
                if height < 0 or height > 100:
                    return {
                        "success": False,
                        "error": f"Invalid height {height} at step {i}",
                        "sequence": sequence,
                        "heights": heights,
                    }

            return {
                "success": True,
                "sequence": sequence,
                "heights": heights,
                "final_height": heights[-1] if heights else 0,
            }

        except Exception as e:
            return {"success": False, "error": str(e), "sequence": sequence}

    def run_parallel_tests(
        self,
        sequences: List[List[Tuple[str, int]]],
        num_workers: int = None,
        checkpoint_file: str = None,
        batch_size: int = 10000,
        start_time: float = None,
        max_seconds: float = None,
    ) -> List[Dict]:
        """Run tests in parallel with checkpointing and time limits."""
        if num_workers is None:
            # Use moderate resource usage - max half of CPU cores
            num_workers = min(4, max(1, mp.cpu_count() // 2))

        print(f"Using {num_workers} worker processes for {len(sequences)} tests")

        results = []
        start_idx = 0

        # Load checkpoint if exists
        if checkpoint_file and os.path.exists(checkpoint_file):
            try:
                with open(checkpoint_file, "r") as f:
                    checkpoint_data = json.load(f)
                    results = checkpoint_data.get("results", [])
                    start_idx = checkpoint_data.get("completed_count", 0)
                    print(
                        f"Resuming from checkpoint: {start_idx}/{len(sequences)} "
                        f"tests completed"
                    )
            except Exception as e:
                print(f"Failed to load checkpoint: {e}, starting fresh")
                start_idx = 0
                results = []

        # Process in batches to avoid memory issues and enable checkpointing
        for batch_start in range(start_idx, len(sequences), batch_size):
            # Check time limit before starting each batch
            if start_time and max_seconds:
                elapsed = time.time() - start_time
                if elapsed > max_seconds:
                    print(
                        f"Time limit reached ({elapsed/60:.2f} minutes), "
                        f"stopping batch processing"
                    )
                    break

            batch_end = min(batch_start + batch_size, len(sequences))
            batch_sequences = sequences[batch_start:batch_end]

            print(
                f"Processing batch {batch_start}-{batch_end} "
                f"({len(batch_sequences)} tests)"
            )

            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                # Submit batch tasks
                future_to_sequence = {
                    executor.submit(self.test_single_sequence, seq): seq
                    for seq in batch_sequences
                }

                # Collect batch results
                batch_results = []
                for i, future in enumerate(as_completed(future_to_sequence)):
                    result = future.result()
                    batch_results.append(result)

                    # Progress indicator and time check
                    if (batch_start + i) % 1000 == 0:
                        if start_time and max_seconds:
                            elapsed = time.time() - start_time
                            remaining = max_seconds - elapsed
                            print(
                                f"Completed {batch_start + i}/{len(sequences)} tests "
                                f"(Elapsed: {elapsed/60:.2f}m, "
                                f"Remaining: {remaining/60:.2f}m)"
                            )

                            # Stop if time limit exceeded
                            if elapsed > max_seconds:
                                print(
                                    f"Time limit reached during batch processing, "
                                    f"stopping"
                                )
                                # Cancel remaining futures
                                for future in future_to_sequence:
                                    future.cancel()
                                break
                        else:
                            print(f"Completed {batch_start + i}/{len(sequences)} tests")

                results.extend(batch_results)

            # Save checkpoint after each batch
            if checkpoint_file:
                checkpoint_data = {
                    "results": results,
                    "completed_count": batch_end,
                    "total_count": len(sequences),
                    "seed": SEED,  # Include seed in checkpoint for reproducibility
                }
                with open(checkpoint_file, "w") as f:
                    json.dump(checkpoint_data, f)
                print(f"Checkpoint saved: {batch_end}/{len(sequences)} tests completed")

            # Final time check after batch
            if start_time and max_seconds:
                elapsed = time.time() - start_time
                if elapsed > max_seconds:
                    print(f"Time limit reached after batch completion, stopping")
                    break

        return results

    def run_comprehensive_tests(self, max_minutes: float = 120.0):
        """Run comprehensive async smoke tests for up to max_minutes."""
        start_time = time.time()
        max_seconds = max_minutes * 60

        print(f"Starting async smoke test suite (max {max_minutes} minutes, seed={SEED})")
        print(
            f"Testing {len(self.tetromino_types)} pieces with "
            f"{sum(len(pos) for pos in self.valid_positions.values())} valid positions"
        )
        print(f"Maximum sequence length: {self.max_sequence_length}")
        print(f"Resource usage: Moderate (max 4 workers, batch processing)")
        print("=" * 80)

        all_results = []

        # Test sequences of increasing length
        for length in range(1, self.max_sequence_length + 1):
            elapsed = time.time() - start_time
            if elapsed > max_seconds:
                print(f"Time limit reached after {elapsed/3600:.2f} hours")
                break

            print(f"\n{length}. Testing sequences of length {length}...")

            # Calculate total combinations for this length
            single_drops = self.generate_all_single_drops()
            total_combinations = len(single_drops) ** length

            # For very large combination spaces, use sampling
            if total_combinations > 1000000:  # More than 1 million combinations
                remaining_time = max_seconds - elapsed
                estimated_time_per_test = 0.001  # 1ms per test estimate
                max_feasible_tests = int(
                    remaining_time / estimated_time_per_test * 0.8
                )  # 80% of remaining time

                if max_feasible_tests < total_combinations:
                    print(
                        f"Total combinations ({total_combinations:,}) exceed time budget"
                    )
                    print(
                        f"Sampling {max_feasible_tests:,} combinations to fit in "
                        f"remaining time"
                    )
                    sequences = self.generate_sequence_combinations(
                        length, sample_size=max_feasible_tests
                    )
                else:
                    sequences = self.generate_sequence_combinations(length)
            else:
                sequences = self.generate_sequence_combinations(length)

            # Use checkpointing for large test sets
            checkpoint_file = f"async_checkpoint_length_{length}.json"
            results = self.run_parallel_tests(
                sequences,
                checkpoint_file=checkpoint_file,
                start_time=start_time,
                max_seconds=max_seconds,
            )
            all_results.extend(results)

            # Clean up checkpoint file after successful completion
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)

            print(f"Completed {len(results)} length-{length} sequence tests")

            # Check if we should continue based on remaining time
            elapsed = time.time() - start_time
            if elapsed > max_seconds * 0.9:  # Stop at 90% of time limit
                print(f"Approaching time limit, stopping at length {length}")
                break

        # Analyze results
        total_tests = len(all_results)
        passed_tests = sum(1 for r in all_results if r.get("success", False))
        failed_tests = total_tests - passed_tests

        print(f"\n=== ASYNC SMOKE TEST SUMMARY ===")
        print(f"Seed used: {SEED}")
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success rate: {passed_tests/total_tests*100:.2f}%")
        print(f"Total time: {(time.time() - start_time)/60:.2f} minutes")

        # Show failures
        failures = [r for r in all_results if not r.get("success", False)]
        if failures:
            print(f"\nFirst 10 failures:")
            for i, failure in enumerate(failures[:10]):
                print(f"{i+1}. {failure.get('error', 'Unknown error')}")
                print(f"   Sequence: {failure.get('sequence', 'Unknown')}")

        # Clean up any remaining checkpoint files
        for length in range(1, self.max_sequence_length + 1):
            checkpoint_file = f"async_checkpoint_length_{length}.json"
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)
                print(f"Cleaned up checkpoint file: {checkpoint_file}")

        # Save detailed results with seed for reproducibility
        with open("async_smoke_test_results.json", "w") as f:
            json.dump(
                {
                    "summary": {
                        "seed": SEED,
                        "total_tests": total_tests,
                        "passed_tests": passed_tests,
                        "failed_tests": failed_tests,
                        "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
                        "duration_minutes": (time.time() - start_time) / 60,
                        "max_sequence_length_tested": min(
                            self.max_sequence_length,
                            length if "length" in locals() else 1,
                        ),
                    },
                    "failures": failures[:100],  # Save first 100 failures
                },
                f,
                indent=2,
            )

        return all_results


def main():
    """Main entry point for async smoke testing."""
    import argparse

    parser = argparse.ArgumentParser(description="Run async Tetris engine smoke tests")
    parser.add_argument(
        "--max-minutes",
        type=float,
        default=120.0,
        help="Maximum minutes to run tests (default: 120.0)",
    )
    parser.add_argument(
        "--max-sequence-length",
        type=int,
        default=8,
        help="Maximum sequence length to test (default: 8, max: 33)",
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=None,
        help="Number of worker processes (default: auto)",
    )

    args = parser.parse_args()

    # Validate sequence length to prevent heights > 100
    if args.max_sequence_length > 33:
        print(
            f"Error: max-sequence-length cannot exceed 33 "
            f"(got {args.max_sequence_length})"
        )
        print("Reason: Longer sequences may result in board heights > 100")
        return 1

    print(f"Using fixed seed: {SEED} for reproducible results")

    suite = TetrisEngineAsyncTestRunner(max_sequence_length=args.max_sequence_length)
    results = suite.run_comprehensive_tests(max_minutes=args.max_minutes)

    # Exit with error code if any tests failed
    failed_count = sum(1 for r in results if not r.get("success", False))
    if failed_count > 0:
        print(f"\nERROR: {failed_count} tests failed!")
        sys.exit(1)
    else:
        print(f"\nSUCCESS: All tests passed with seed {SEED}")
        sys.exit(0)


if __name__ == "__main__":
    main()
