# Tetris Engine - Solution Package

This repository contains a complete solution to the Tetris drop simulation problem, intented to be implemented as a (simulated) production-ready Python package. This README serves as the comprehensive instruction manual for using, testing, and maintaining the solution for the take-home assignment, simulating a team manual for onboarding and maintenance.

(adopted markdown emojis from [Github gist](https://gist.github.com/rxaviers/7360908))

## 🎯 Problem Solution

This package implements a simplified Tetris engine that:
- Processes sequences of **7 Tetromino types** (I, T, S, Z, Q, L, J) with no rotations
- Simulates piece drops on a **10-column board** with unbounded height
- Handles **line clearing** by removing full rows and shifting remaining pieces down
- Calculates final board height after each sequence

## 📁 Project Structure

```
tetris/
├── src/tetris_engine/               # Core solution implementation
│   ├── __init__.py
│   ├── constants.py                 # Tetromino definitions and board constants
│   ├── core.py                      # Board logic and drop simulation
│   ├── io.py                        # Input/output processing and play each tetris sequence
using the Board class.
│   └── main.py                      # CLI entry point
├── tests/                           # 🧪 Comprehensive test suite
│   ├── test_core.py                 # Core algorithm tests
│   └── test_io.py                   # I/O processing tests
├── docs/                            # 📚 Documentation regarding API and architecture
│   ├── _sphinx/                     # Sphinx documentation source and artifacts
│   │   ├── _build/html/             # Generated HTML documentation
│   │   ├── conf.py                  # Sphinx configuration
│   │   ├── index.rst                # Documentation index
│   │   └── api.rst                  # API reference
│   └── architecture.md              # Design decisions and trade-offs
├── examples/                        # 📋 Sample inputs and usage examples
│   └── sample_input.txt             # The sample Test case sent by HR (from Bridget's email).
├── scripts/                         # 🔧 Utility scripts
│   └── run_example.sh               # Quick demo script
│   └── run_exhaustive_tests.sh      # Quick demo script
│   └── monitor_exhaustive_tests.sh  # Quick demo script
├── .github/workflows/               # 🚢 CI/CD pipeline (synthetic demo)
│   └── ci.yml                       # "Production-ready" workflow example
├── Makefile                         # 🛠️ Development automation
├── pyproject.toml                   # 📦 Package configuration
└── README.MD                        # 📖 This instruction manual
```

### Code Organization Philosophy

- **`src/tetris_engine/`**: Contains the core solution that directly addresses the problem requirements
- **Everything else**: Industry standard Python project development tools that simulate real-world team environments, enabling effortless code ownership transfer and maintenance

This structure allows teams to:
- **Onboard quickly** using comprehensive documentation and tests
- **Maintain confidently** with automated testing and linting
- **Deploy safely** with CI/CD pipelines and quality gates
- **Understand deeply** through architecture documentation and code comments

## 🚀 Quick Start

### 1. Installation

```bash
make install
```

This creates a virtual environment, upgrades pip, and installs the package with all development dependencies.

### 2. Run the Example

```bash
chmod +x scripts/run_example.sh
./scripts/run_example.sh
```

### 3. Use with Custom Input

```bash
# Output to console
tetris-engine examples/sample_input.txt

# Output to file
mkdir ./out

tetris-engine examples/sample_input.txt out/output.txt
```

## 📋 Input/Output Format

### Input Format
Each line contains a comma-separated sequence of piece drops. Each drop is specified as:
- **Letter**: Tetromino type (I, T, S, Z, Q, L, J)
- **Digit**: Leftmost column position (0-9)

Example: `Q0,Q2,Q4,Q6,Q8` drops five Q pieces at columns 0, 2, 4, 6, and 8.

### Input Constraints
**Important**: This implementation is designed for inputs that result in **maximum board height ≤ 100**.

- **Board dimensions**: 10 columns (0-9) × unbounded height (capped at 100)
- **Maximum height constraint**: We can assume implicit input sequence constaint that the final board height will not exceed 100 rows
- **Sequence length**: Recommended maximum 33 pieces per sequence to stay within height limits (each piece at most height of 3)
- **Piece placement**: All pieces must fit within the 10-column board width

**Rationale**: The height constraint ensures optimal performance and memory usage. Sequences that would exceed this limit are considered invalid input and may produce undefined behavior.

### Output Format
One integer per line representing the final board height after processing each input sequence.

### Sample Input
See `examples/sample_input.txt` from the instruction (Bridget's) email for comprehensive test cases covering:
- Single piece drops
- Line clearing scenarios
- Complex stacking patterns
- Edge cases and boundary conditions

## 🧪 Testing & Quality Assurance

### Run Tests
```bash
make test                    # Run standard test suite with coverage
make test-parallel           # Run tests in parallel for speed
make lint                    # Check code quality
make lint-fix               # Auto-fix formatting issues
make test-exhaustive-quick  # Run generated test cases (30 minutes)
make test-exhaustive        # Run full generated test cases (120 minutes)
```

**Unit tests** under the `tests/` directory provide core functionality validation and cover essential base cases. These tests are designed for fast execution and include:
- Single piece placement at all valid positions with exact height validation
- Boundary condition testing for all piece types
- Basic piece stacking combinations
- Line clearing patterns
- I/O processing validation

**Note**: Unit tests cannot be exhaustive due to the vast combination space (7^n sequences). They focus on critical functionality and known edge cases for rapid development feedback.

**Async smoke tests** in `scripts/sample_async_smoke_test.py` are designed for comprehensive correctness checking:
- **Fixed seed testing**: Uses `SEED=42` for reproducible results across runs
- **Sequence generation**: Tests increasing sequence lengths with parallel execution
- **CI/CD deployment**: Detects output changes on fixed input (random seed)
- **Async smoke testing**: Extensive validation suitable for CI/CD pipelines
- **Resource management**: Intelligent load balancing and checkpointing

This approach separates fast unit testing from comprehensive validation, enabling both rapid development feedback and thorough correctness verification.

**Large-Scale Testing Strategy**

For production environments requiring massive test coverage, the testing framework supports distributed execution:

**Batch Processing Approach:**
- Split large test files into smaller batches (e.g., 1000 lines per file)
- Spawn multiple program instances to process batches in parallel
- Each line represents an independent game state, enabling perfect parallelization
- Aggregate results from all batch processes for comprehensive validation

**Resource Management:**
- Current implementation provides sufficient coverage for the time invested in this problem.
- In the real production environments, intelligent load balancing would dynamically spawn the optimal number of board instances based on:
  - Available CPU cores and memory
  - Input complexity and expected processing time
  - System load and resource constraints

This approach scales from single-machine parallel processing to distributed computing clusters, making it suitable for enterprise-level validation scenarios.

### Code Quality Tools
The project uses the following tools to merely ensure code quality and maintainability, while none of them is required to run the algorithm lib itself (`tetris_engine` from cli or `python -m tetris_engine.main`):
- **pytest**: Test framework with coverage reporting
- **flake8**: Style and error checking
- **mypy**: Static type checking
- **black**: Code formatting
- **isort**: Import sorting
- **autopep8**: PEP 8 compliance

## 📚 Documentation

### API Documentation with Sphinx
This project uses Sphinx to auto-generate API documentation from docstrings, following the same practices as popular Python libraries like PyTorch and NumPy.

```bash
make docs
```

**Expected Output:**
- Generated HTML documentation at `docs/_sphinx/_build/html/index.html`
- Complete API reference with function signatures and docstrings
- Cross-referenced links between modules and classes

**In Real Work:**
The generated HTML documentation would typically be hosted on a web service (like Read the Docs, GitHub Pages, or internal documentation servers) for easy access by team members and users.

### Architecture Documentation
See `docs/architecture.md` for:
- Algorithm design decisions
- Performance trade-offs
- Implementation details with source code references

## 🔧 Development

### Project Setup
```bash
git clone <repository-url> # synthetic example
cd tetris
make install
```

### Development Workflow
1. **Make changes** to source code
2. **Run tests**: `make test`
3. **Check quality**: `make lint-fix && make lint`
4. **Update docs**: `make docs` (if API changes)
5. **Commit changes** with descriptive messages

### Debugging
```bash
# Activate virtual environment
. .venv_tetris/bin/activate

# Debug with pdb
python -m pdb -m tetris_engine.main examples/sample_input.txt

# Run module directly
python -m tetris_engine.main examples/sample_input.txt
```

## 🚀 CI/CD Pipeline

The project includes a comprehensive CI/CD pipeline (`.github/workflows/ci.yml`) that demonstrates production-ready practices:

- **Code Quality**: Automated linting and formatting
- **Multi-version Testing**: Python 3.8-3.12 compatibility
- **Security Scanning**: Vulnerability detection
- **Documentation**: Automated doc generation
- **Package Building**: Distribution preparation
- **Deployment**: Staging and production workflows

*Note: This pipeline is synthetic, only for demonstration purposes in this take-home assignment.*

## 📖 Additional Resources

- **Architecture Guide**: `docs/architecture.md` - Design decisions and trade-offs
- **API Reference**: Generated Sphinx documentation in `docs/_sphinx/_build/html/`
- **Test Examples**: `tests/` directory for usage patterns
- **Sample Data**: `examples/sample_input.txt` for testing

## 🤝 Contributing

This project follows professional development practices:
1. All code must pass tests and linting
2. New features require corresponding tests
3. Documentation must be updated for API changes
4. Follow the existing code style and patterns

## 📄 Dependencies

### Core Library
- **Zero external dependencies** - Pure Python implementation

### Development & Testing
- **Required**: `pytest`, `pytest-cov` (for running tests)
- **Optional**: `flake8`, `mypy`, `black`, `isort`, `autopep8` (for code quality)
- **Documentation**: `sphinx`, `sphinx-rtd-theme` (for API docs)

The core library can be used independently without any external dependencies, making it suitable for deployment in constrained environments.

## 🎯 For Fresh Users

If you're new to this project, follow this step-by-step guide:

1. **Clone and setup**: `git clone <repo> && cd tetris && make install`
2. **Try the example**: `./scripts/run_example.sh`
3. **Run tests**: `make test` to verify everything works
4. **Read the docs**: Check `docs/architecture.md` for design details
5. **Explore the code**: Start with `src/tetris_engine/core.py` for the main logic
6. **Make changes**: Follow the development workflow above

The project is designed to be self-documenting and easy to understand. Each component has clear responsibilities and comprehensive tests to guide your understanding.

## ⏱️ Development Time

**Total effective time to complete this exercise: 4 hours**

- **1 hour**: Writing the robust solution (with iteration and refinement)
- **1.5 hours**: Writing comprehensive tests (unit tests, script tests, edge cases)
- **1 hour**: Project setup for production-ready environment (build tools, CI, docs, git)
- **0.5 hours**: Final polishing and documentation

## 📄 License

No license is included for this take-home assignment as it is for internal evaluation purposes only. The team may need to add an appropriate license once they decide to publish this solution.