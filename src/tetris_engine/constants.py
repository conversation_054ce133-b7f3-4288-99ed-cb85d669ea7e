"""
Tetromino definitions and board constants.

Coordinate System:
- x-axis (columns): 0 = leftmost, increases rightward: ">".
- y-axis (rows):    0 = bottom,   increases upward: "^".

Tetromino footprints are defined as offsets from the leftmost column.

Testing Constants:
- SEED: Fixed random seed for reproducible test generation and async testing.
"""

TETROMINO = {
    "I": [(0, 0), (1, 0), (2, 0), (3, 0)],
    "T": [(0, 1), (1, 1), (2, 1), (1, 0)],
    "S": [(1, 1), (2, 1), (0, 0), (1, 0)],
    "Z": [(0, 1), (1, 1), (1, 0), (2, 0)],
    "Q": [(0, 0), (1, 0), (0, 1), (1, 1)],
    "L": [(0, 0), (0, 1), (0, 2), (1, 0)],
    "J": [(1, 0), (1, 1), (1, 2), (0, 0)],
}
WIDTH = 10
MAX_HEIGHT = 100

# Testing constants
SEED = 42  # Fixed seed for reproducible test generation

# Single piece heights (when dropped on empty board)
SINGLE_PIECE_HEIGHTS = {
    "I": 1,  # I piece: 4x1 horizontal
    "T": 2,  # T piece: 3x2 with stem
    "S": 2,  # S piece: 3x2 zigzag
    "Z": 2,  # Z piece: 3x2 zigzag
    "Q": 2,  # Q piece: 2x2 square
    "L": 3,  # L piece: 2x3 with foot
    "J": 3,  # J piece: 2x3 with foot
}
