"""
Tetromino definitions and board constants.

Coordinate System:
- x-axis (columns): 0 = leftmost, increases rightward: ">".
- y-axis (rows):    0 = bottom,   increases upward: "^".

Tetromino footprints are defined as offsets from the leftmost column.
"""

TETROMINO = {
    "I": [(0, 0), (1, 0), (2, 0), (3, 0)],
    "T": [(0, 1), (1, 1), (2, 1), (1, 0)],
    "S": [(1, 1), (2, 1), (0, 0), (1, 0)],
    "Z": [(0, 1), (1, 1), (1, 0), (2, 0)],
    "Q": [(0, 0), (1, 0), (0, 1), (1, 1)],
    "L": [(0, 0), (0, 1), (0, 2), (1, 0)],
    "J": [(1, 0), (1, 1), (1, 2), (0, 0)],
}
WIDTH = 10
MAX_HEIGHT = 100

