# Architecture & Design Notes

## Coordinate System

- **x-axis (columns):** 0 = leftmost → 9 = rightmost  
- **y-axis (rows):**    0 = bottom   → upwards

## Core Components

- **constants.py:**
  - `TETROMINO`: footprint offsets for each piece
  - `WIDTH`: board width (10 columns)
  - `MAX_HEIGHT`: maximum allowed board height (100 rows)
  - `SEED`: fixed random seed for reproducible testing (42)
  - `SINGLE_PIECE_HEIGHTS`: expected heights for single piece drops

- **core.py:**  
  - `Board` class  
    - `.occupied`: `set[(x,y)]` of locked cells  
    - `.row_count`: `dict[row → count]`  
    - `.col_heights`: `List[int]` of max y per column  
    - `.drop()`: gravity, placement, clear, height return

- **io.py:**  
  - `process_file()`: line-by-line input → independent `Board` runs → output heights  
  - `main()`: CLI wrapper using `argparse`

## Input Constraints & Design Rationale

### Height Constraint: MAX_HEIGHT = 100
This implementation enforces a **maximum board height of 100 rows** for the following reasons:

1. **Performance Optimization**: With height ≤ 100, the maximum number of occupied cells is bounded (B ≤ 1,000), ensuring predictable performance.

2. **Memory Management**: Bounded height prevents unbounded memory growth in pathological cases.

3. **Algorithm Efficiency**: The chosen O(B·R) line-clearing algorithm performs optimally within this constraint.

4. **Test Coverage**: Enables comprehensive testing of all realistic input scenarios within reasonable time limits.

**Sequence Length Guidelines**: To stay within the height constraint, input sequences should typically contain ≤30-35 pieces. The test generation system automatically validates this constraint.

## Algorithm Implementation Trade-offs
Here, we use the following notation:
B = total number of occupied cells (blocks) on the board
R = number of full rows cleared in a single clear operation

- **Rebuild-on-clear (chosen):**
  - After lock-in, recompute `row_count` (O(B)),
    filter & shift `occupied` (O(B·R)),
    recompute `col_heights` (O(B)).
  - Simpler, fast for H≤100 (B≤1,000; R small).

- **Two-pointer approach (considered):**
  - Sort rows & shift counts in linear passes,
    overall O(B log B + R + B).
  - More complex, only wins for very large boards/mass clears.

Although I attempted to consider an O(B log B + R + B) two-pointer algorithm (sorting rows and shifting counts with two indices), for a capped max height ≤100 (B ≤1,000 cells, R very small),
the straightforward O(B·R) clear logic is both simpler and faster in practice, which I finally adopted.

## Testing Architecture

### Two-Tier Testing Strategy

**Unit Tests** (`tests/test_multi_drops.py`):
- **TetrisEngineUnitTestMultiDropRunner**: Manages fast unit testing
- Single piece placement with exact height validation using `SINGLE_PIECE_HEIGHTS`
- Boundary conditions and stacking combinations
- Designed for rapid development feedback (< 1 second execution)

**Async Tests** (`scripts/sample_async_test.py`):
- **TetrisEngineAsyncTestRunner**: Manages comprehensive sequence testing
- Fixed seed (`SEED=42`) for reproducible results across CI runs
- Parallel execution with intelligent resource management
- Checkpointing for long-running tests
- Designed for overnight CI validation (30-120 minutes)

This separation enables both rapid development iteration and thorough correctness verification without compromising either speed or coverage.

## Testing

- **pytest** suite under `tests/` covers all piece types, boundary drops, stacking, clears, and I/O.
