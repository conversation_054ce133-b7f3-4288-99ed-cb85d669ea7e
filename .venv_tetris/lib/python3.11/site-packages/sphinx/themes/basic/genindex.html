{# Template for an "all-in-one" index. #}
{%- extends "layout.html" %}
{% set title = _('Index') %}

{% macro indexentries(firstname, links) %}
  {%- if links -%}
    <a href="{{ links[0][1] }}">
    {%- if links[0][0] %}<strong>{% endif -%}
    {{ firstname|e }}
    {%- if links[0][0] %}</strong>{% endif -%}
    </a>

    {%- for ismain, link in links[1:] -%}
      , <a href="{{ link }}">{% if ismain %}<strong>{% endif -%}
      [{{ loop.index }}]
      {%- if ismain %}</strong>{% endif -%}
      </a>
    {%- endfor %}
  {%- else %}
    {{ firstname|e }}
  {%- endif %}
{% endmacro %}

{% block body %}

<h1 id="index">{{ _('Index') }}</h1>

<div class="genindex-jumpbox">
 {% for key, dummy in genindexentries -%}
 <a href="#{{ key }}"><strong>{{ key }}</strong></a>
 {% if not loop.last %}| {% endif %}
 {%- endfor %}
</div>

{%- for key, entries in genindexentries %}
<h2 id="{{ key }}">{{ key }}</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  {%- for column in entries|slice_index(2) if column %}
  <td style="width: 33%; vertical-align: top;"><ul>
    {%- for entryname, (links, subitems, _) in column %}
      <li>{{ indexentries(entryname, links) }}
      {%- if subitems %}
      <ul>
      {%- for subentryname, subentrylinks in subitems %}
        <li>{{ indexentries(subentryname, subentrylinks) }}</li>
      {%- endfor %}
      </ul>
      {%- endif -%}</li>
    {%- endfor %}
  </ul></td>
  {%- endfor %}
</tr></table>
{% endfor %}

{% endblock %}

{% block sidebarrel %}
{% if split_index %}
   <h4>{{ _('Index') }}</h4>
   <p>{% for key, dummy in genindexentries -%}
   <a href="{{ pathto('genindex-' + key) }}"><strong>{{ key }}</strong></a>
     {% if not loop.last %}| {% endif %}
   {%- endfor %}</p>

   <p><a href="{{ pathto('genindex-all') }}"><strong>{{ _('Full index on one page') }}</strong></a></p>
{% endif %}
   {{ super() }}
{% endblock %}
