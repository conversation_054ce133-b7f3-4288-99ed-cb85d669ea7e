SwedishStemmer=function(){var r=new BaseStemmer;var e=[["a",-1,1],["arna",0,1],["erna",0,1],["heterna",2,1],["orna",0,1],["ad",-1,1],["e",-1,1],["ade",6,1],["ande",6,1],["arne",6,1],["are",6,1],["aste",6,1],["en",-1,1],["anden",12,1],["aren",12,1],["heten",12,1],["ern",-1,1],["ar",-1,1],["er",-1,1],["heter",18,1],["or",-1,1],["s",-1,2],["as",21,1],["arnas",22,1],["ernas",22,1],["ornas",22,1],["es",21,1],["ades",26,1],["andes",26,1],["ens",21,1],["arens",29,1],["hetens",29,1],["erns",21,1],["at",-1,1],["andet",-1,1],["het",-1,1],["ast",-1,1]];var a=[["dd",-1,-1],["gd",-1,-1],["nn",-1,-1],["dt",-1,-1],["gt",-1,-1],["kt",-1,-1],["tt",-1,-1]];var i=[["ig",-1,1],["lig",0,1],["els",-1,1],["fullt",-1,3],["löst",-1,2]];var t=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,24,0,32];var s=[119,127,149];var u=0;var n=0;function c(){n=r.limit;var e=r.cursor;{var a=r.cursor+3;if(a>r.limit){return false}r.cursor=a}u=r.cursor;r.cursor=e;r:while(true){var i=r.cursor;e:{if(!r.in_grouping(t,97,246)){break e}r.cursor=i;break r}r.cursor=i;if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){e:{if(!r.out_grouping(t,97,246)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}n=r.cursor;r:{if(!(n<u)){break r}n=u}return true}function l(){var a;if(r.cursor<n){return false}var i=r.limit_backward;r.limit_backward=n;r.ket=r.cursor;a=r.find_among_b(e);if(a==0){r.limit_backward=i;return false}r.bra=r.cursor;r.limit_backward=i;switch(a){case 1:if(!r.slice_del()){return false}break;case 2:if(!r.in_grouping_b(s,98,121)){return false}if(!r.slice_del()){return false}break}return true}function o(){if(r.cursor<n){return false}var e=r.limit_backward;r.limit_backward=n;var i=r.limit-r.cursor;if(r.find_among_b(a)==0){r.limit_backward=e;return false}r.cursor=r.limit-i;r.ket=r.cursor;if(r.cursor<=r.limit_backward){r.limit_backward=e;return false}r.cursor--;r.bra=r.cursor;if(!r.slice_del()){return false}r.limit_backward=e;return true}function f(){var e;if(r.cursor<n){return false}var a=r.limit_backward;r.limit_backward=n;r.ket=r.cursor;e=r.find_among_b(i);if(e==0){r.limit_backward=a;return false}r.bra=r.cursor;switch(e){case 1:if(!r.slice_del()){return false}break;case 2:if(!r.slice_from("lös")){return false}break;case 3:if(!r.slice_from("full")){return false}break}r.limit_backward=a;return true}this.stem=function(){var e=r.cursor;c();r.cursor=e;r.limit_backward=r.cursor;r.cursor=r.limit;var a=r.limit-r.cursor;l();r.cursor=r.limit-a;var i=r.limit-r.cursor;o();r.cursor=r.limit-i;var t=r.limit-r.cursor;f();r.cursor=r.limit-t;r.cursor=r.limit_backward;return true};this["stemWord"]=function(e){r.setCurrent(e);this.stem();return r.getCurrent()}};