Documentation.addTranslations({
    "locale": "uk_UA",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": "",
        "About these documents": "\u041f\u0440\u043e \u0446\u0456 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0438",
        "Automatically generated list of changes in version %(version)s": "\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u043d\u043e\u0433\u043e \u0437\u0433\u0435\u043d\u0435\u0440\u043e\u0432\u0430\u043d\u0438\u0439 \u0441\u043f\u0438\u0441\u043e\u043a \u0437\u043c\u0456\u043d \u0432 \u0432\u0435\u0440\u0441\u0456\u0457 %(version)s",
        "C API changes": "\u0437\u043c\u0456\u043d\u0438 C API",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "",
        "Collapse sidebar": "",
        "Complete Table of Contents": "\u041f\u043e\u0432\u043d\u0438\u0439 \u0417\u043c\u0456\u0441\u0442",
        "Contents": "",
        "Copyright": "\u0410\u0432\u0442\u043e\u0440\u0441\u044c\u043a\u0456 \u043f\u0440\u0430\u0432\u0430",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "",
        "Full index on one page": "\u041f\u043e\u0432\u043d\u0438\u0439 \u0456\u043d\u0434\u0435\u043a\u0441 \u043d\u0430 \u043e\u0434\u043d\u0456\u0439 \u0441\u0442\u043e\u0440\u0456\u043d\u0446\u0456",
        "General Index": "\u0417\u0430\u0433\u0430\u043b\u044c\u043d\u0438\u0439 \u0456\u043d\u0434\u0435\u043a\u0441",
        "Global Module Index": "\u0417\u0430\u0433\u0430\u043b\u044c\u043d\u0438\u0439 \u0456\u043d\u0434\u0435\u043a\u0441 \u043c\u043e\u0434\u0443\u043b\u0456\u0432",
        "Go": "\u0412\u043f\u0435\u0440\u0435\u0434",
        "Hide Search Matches": "\u041f\u0440\u0438\u0445\u043e\u0432\u0430\u0442\u0438 \u0441\u043f\u0456\u0432\u043f\u0430\u0434\u0456\u043d\u043d\u044f \u043f\u043e\u0448\u0443\u043a\u0443",
        "Index": "\u0406\u043d\u0434\u0435\u043a\u0441",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "\u0406\u043d\u0434\u0435\u043a\u0441\u043d\u0456 \u0441\u0442\u043e\u0440\u0456\u043d\u043a\u0438 \u043f\u043e \u0441\u0438\u043c\u0432\u043e\u043b\u0430\u043c",
        "Indices and tables:": "\u0406\u043d\u0434\u0435\u043a\u0441\u0438 \u0442\u0430 \u0442\u0430\u0431\u043b\u0438\u0446\u0456:",
        "Last updated on %(last_updated)s.": "\u0412\u043e\u0441\u0442\u0430\u043d\u043d\u0454 \u043e\u043d\u043e\u0432\u043b\u0435\u043d\u043e %(last_updated)s.",
        "Library changes": "\u0417\u043c\u0456\u043d\u0438 \u0432 \u0431\u0456\u0431\u043b\u0456\u043e\u0442\u0435\u0446\u0456",
        "Navigation": "\u041d\u0430\u0432\u0456\u0433\u0430\u0446\u0456\u044f",
        "Next topic": "\u041d\u0430\u0441\u0442\u0443\u043f\u043d\u0430 \u0442\u0435\u043c\u0430",
        "Other changes": "\u0406\u043d\u0448\u0456 \u0437\u043c\u0456\u043d\u0438",
        "Overview": "\u041e\u0433\u043b\u044f\u0434",
        "Please activate JavaScript to enable the search\n    functionality.": "\u0411\u0443\u0434\u044c-\u043b\u0430\u0441\u043a\u0430 \u0432\u0456\u043c\u043a\u043d\u0456\u0442\u044c \u043f\u0456\u0434\u0442\u0440\u0438\u043c\u043a\u0443 JavaScript, \u0449\u043e\u0431 \u0432\u0432\u0456\u043a\u043d\u0443\u0442\u0438\n\"\n\"    \u043f\u043e\u0448\u0443\u043a.",
        "Preparing search...": "",
        "Previous topic": "\u041f\u043e\u043f\u0435\u0440\u0435\u0434\u043d\u0456\u0439 \u0440\u043e\u0437\u0434\u0456\u043b",
        "Quick search": "\u0428\u0432\u0438\u0434\u043a\u0438\u0439 \u043f\u043e\u0448\u0443\u043a",
        "Search": "\u041f\u043e\u0448\u0443\u043a",
        "Search Page": "\u0421\u0442\u043e\u0440\u0456\u043d\u043a\u0430 \u043f\u043e\u0448\u0443\u043a\u0443",
        "Search Results": "\u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442\u0438 \u043f\u043e\u0448\u0443\u043a\u0443",
        "Search finished, found one page matching the search query.": [
            "",
            "",
            "",
            ""
        ],
        "Search within %(docstitle)s": "\u0428\u0443\u043a\u0430\u0442\u0438 \u0432 %(docstitle)s",
        "Searching": "",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "\u0412\u0456\u0434\u043e\u0431\u0440\u0430\u0437\u0438\u0442\u0438 \u0432\u0438\u0445\u0456\u0434\u043d\u0438\u0439 \u0442\u0435\u043a\u0441\u0442",
        "Table of Contents": "",
        "This Page": "\u0426\u044f \u0441\u0442\u043e\u0440\u0456\u043d\u043a\u0430",
        "Welcome! This is": "",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "",
        "all functions, classes, terms": "\u0432\u0441\u0456 \u0444\u0443\u043d\u043a\u0446\u0456\u0457, \u043a\u043b\u0430\u0441\u0438, \u0442\u0435\u0440\u043c\u0456\u043d\u0438",
        "can be huge": "\u043c\u043e\u0436\u0435 \u0431\u0443\u0442\u0438 \u0432\u0435\u043b\u0438\u0447\u0435\u0437\u043d\u0438\u043c",
        "last updated": "",
        "lists all sections and subsections": "\u043f\u0435\u0440\u0435\u043b\u0456\u0447\u0438\u0442\u0438 \u0432\u0441\u0456 \u0441\u0435\u043a\u0446\u0456\u0457 \u0442\u0430 \u043f\u0456\u0434\u0441\u0435\u043a\u0446\u0456\u0457",
        "next chapter": "\u043d\u0430\u0441\u0442\u0443\u043f\u043d\u0438\u0439 \u0440\u043e\u0437\u0434\u0456\u043b",
        "previous chapter": "\u041f\u043e\u043f\u0435\u0440\u0435\u0434\u043d\u0456\u0439 \u0440\u043e\u0437\u0434\u0456\u043b",
        "quick access to all modules": "\u0448\u0432\u0438\u0434\u043a\u0438\u0439 \u0434\u043e\u0441\u0442\u0443\u043f \u0434\u043e \u0432\u0441\u0456\u0445 \u043c\u043e\u0434\u0443\u043b\u0456\u0432",
        "search": "\u043f\u043e\u0448\u0443\u043a",
        "search this documentation": "\u0448\u0443\u043a\u0430\u0442\u0438 \u0446\u044e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0456\u044e",
        "the documentation for": ""
    },
    "plural_expr": "(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3)"
});