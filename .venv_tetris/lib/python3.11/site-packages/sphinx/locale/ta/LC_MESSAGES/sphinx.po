# Translations template for Sphinx.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-08-17 14:58+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Tamil (http://app.transifex.com/sphinx-doc/sphinx-1/language/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr ""

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr ""

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr ""

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr ""

#: sphinx/application.py:223
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr ""

#: sphinx/application.py:239
msgid "making output directory"
msgstr ""

#: sphinx/application.py:244 sphinx/registry.py:444
#, python-format
msgid "while setting up extension %s:"
msgstr ""

#: sphinx/application.py:250
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr ""

#: sphinx/application.py:281
#, python-format
msgid "loading translations [%s]... "
msgstr ""

#: sphinx/application.py:298 sphinx/util/display.py:84
msgid "done"
msgstr ""

#: sphinx/application.py:300
msgid "not available for built-in messages"
msgstr ""

#: sphinx/application.py:314
msgid "loading pickled environment"
msgstr ""

#: sphinx/application.py:322
#, python-format
msgid "failed: %s"
msgstr ""

#: sphinx/application.py:336
msgid "No builder selected, using default: html"
msgstr ""

#: sphinx/application.py:369
msgid "succeeded"
msgstr ""

#: sphinx/application.py:370
msgid "finished with problems"
msgstr ""

#: sphinx/application.py:374
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr ""

#: sphinx/application.py:376
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr ""

#: sphinx/application.py:379
#, python-format
msgid "build %s, %s warning."
msgstr ""

#: sphinx/application.py:381
#, python-format
msgid "build %s, %s warnings."
msgstr ""

#: sphinx/application.py:385
#, python-format
msgid "build %s."
msgstr ""

#: sphinx/application.py:616
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr ""

#: sphinx/application.py:695
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr ""

#: sphinx/application.py:717 sphinx/application.py:739
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr ""

#: sphinx/application.py:1288
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr ""

#: sphinx/application.py:1292
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr ""

#: sphinx/application.py:1295
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr ""

#: sphinx/application.py:1299
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr ""

#: sphinx/application.py:1307 sphinx/application.py:1311
#, python-format
msgid "doing serial %s"
msgstr ""

#: sphinx/config.py:179
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr ""

#: sphinx/config.py:188
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr ""

#: sphinx/config.py:217
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr ""

#: sphinx/config.py:226
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr ""

#: sphinx/config.py:231
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr ""

#: sphinx/config.py:260
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr ""

#: sphinx/config.py:288
#, python-format
msgid "No such config value: %s"
msgstr ""

#: sphinx/config.py:312
#, python-format
msgid "Config value %r already present"
msgstr ""

#: sphinx/config.py:360
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr ""

#: sphinx/config.py:363
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr ""

#: sphinx/config.py:370
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr ""

#: sphinx/config.py:393
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr ""

#: sphinx/config.py:413
#, python-format
msgid "Section %s"
msgstr ""

#: sphinx/config.py:414
#, python-format
msgid "Fig. %s"
msgstr ""

#: sphinx/config.py:415
#, python-format
msgid "Table %s"
msgstr ""

#: sphinx/config.py:416
#, python-format
msgid "Listing %s"
msgstr ""

#: sphinx/config.py:488
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr ""

#: sphinx/config.py:506
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr ""

#: sphinx/config.py:518
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr ""

#: sphinx/config.py:528
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr ""

#: sphinx/config.py:540
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr ""

#: sphinx/events.py:63
#, python-format
msgid "Event %r already present"
msgstr ""

#: sphinx/events.py:69
#, python-format
msgid "Unknown event name: %s"
msgstr ""

#: sphinx/events.py:107
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr ""

#: sphinx/extension.py:53
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr ""

#: sphinx/extension.py:69
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr ""

#: sphinx/highlighting.py:149
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr ""

#: sphinx/highlighting.py:176
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: sphinx/project.py:65
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr ""

#: sphinx/project.py:74
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: sphinx/registry.py:136
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr ""

#: sphinx/registry.py:138
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr ""

#: sphinx/registry.py:151
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr ""

#: sphinx/registry.py:158
#, python-format
msgid "Builder name %s not registered"
msgstr ""

#: sphinx/registry.py:165
#, python-format
msgid "domain %s already registered"
msgstr ""

#: sphinx/registry.py:188 sphinx/registry.py:201 sphinx/registry.py:212
#, python-format
msgid "domain %s not yet registered"
msgstr ""

#: sphinx/registry.py:192
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr ""

#: sphinx/registry.py:204
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr ""

#: sphinx/registry.py:215
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr ""

#: sphinx/registry.py:246
#, python-format
msgid "The %r object_type is already registered"
msgstr ""

#: sphinx/registry.py:272
#, python-format
msgid "The %r crossref_type is already registered"
msgstr ""

#: sphinx/registry.py:279
#, python-format
msgid "source_suffix %r is already registered"
msgstr ""

#: sphinx/registry.py:288
#, python-format
msgid "source_parser for %r is already registered"
msgstr ""

#: sphinx/registry.py:296
#, python-format
msgid "Source parser for %s not registered"
msgstr ""

#: sphinx/registry.py:312
#, python-format
msgid "Translator for %r already exists"
msgstr ""

#: sphinx/registry.py:328
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr ""

#: sphinx/registry.py:411
#, python-format
msgid "enumerable_node %r already registered"
msgstr ""

#: sphinx/registry.py:423
#, python-format
msgid "math renderer %s is already registered"
msgstr ""

#: sphinx/registry.py:438
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr ""

#: sphinx/registry.py:449
msgid "Original exception:\n"
msgstr ""

#: sphinx/registry.py:450
#, python-format
msgid "Could not import extension %s"
msgstr ""

#: sphinx/registry.py:455
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr ""

#: sphinx/registry.py:464
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr ""

#: sphinx/registry.py:472
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr ""

#: sphinx/roles.py:178
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr ""

#: sphinx/roles.py:194
#, python-format
msgid "invalid PEP number %s"
msgstr ""

#: sphinx/roles.py:228
#, python-format
msgid "invalid RFC number %s"
msgstr ""

#: sphinx/theming.py:77
#, python-format
msgid "theme %r doesn't have \"theme\" setting"
msgstr ""

#: sphinx/theming.py:79
#, python-format
msgid "theme %r doesn't have \"inherit\" setting"
msgstr ""

#: sphinx/theming.py:85
#, python-format
msgid "no theme named %r found, inherited by %r"
msgstr ""

#: sphinx/theming.py:108
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr ""

#: sphinx/theming.py:127
#, python-format
msgid "unsupported theme option %r given"
msgstr ""

#: sphinx/theming.py:216
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr ""

#: sphinx/theming.py:230
#, python-format
msgid "no theme named %r found (missing theme.conf?)"
msgstr ""

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr ""

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr ""

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr ""

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:575
#: sphinx/builders/__init__.py:602
msgid "writing output... "
msgstr ""

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr ""

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr ""

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr ""

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr ""

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr ""

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr ""

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr ""

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:236
#, python-format
msgid "building [%s]: "
msgstr ""

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr ""

#: sphinx/builders/__init__.py:321
#, python-format
msgid "%d found"
msgstr "%d கண்டு ப்பிடித்த விட்டது"

#: sphinx/builders/__init__.py:323
msgid "none found"
msgstr ""

#: sphinx/builders/__init__.py:328
msgid "pickling environment"
msgstr ""

#: sphinx/builders/__init__.py:334
msgid "checking consistency"
msgstr ""

#: sphinx/builders/__init__.py:338
msgid "no targets are out of date."
msgstr ""

#: sphinx/builders/__init__.py:377
msgid "updating environment: "
msgstr ""

#: sphinx/builders/__init__.py:398
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr ""

#: sphinx/builders/__init__.py:436 sphinx/builders/__init__.py:448
msgid "reading sources... "
msgstr ""

#: sphinx/builders/__init__.py:550
#, python-format
msgid "docnames to write: %s"
msgstr ""

#: sphinx/builders/__init__.py:559 sphinx/builders/singlehtml.py:155
msgid "preparing documents"
msgstr ""

#: sphinx/builders/__init__.py:562
msgid "copying assets"
msgstr ""

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr ""

#: sphinx/builders/_epub_base.py:403 sphinx/builders/html/__init__.py:750
#: sphinx/builders/latex/__init__.py:425 sphinx/builders/texinfo.py:183
msgid "copying images... "
msgstr ""

#: sphinx/builders/_epub_base.py:410
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr ""

#: sphinx/builders/_epub_base.py:416 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:433 sphinx/builders/texinfo.py:193
#, python-format
msgid "cannot copy image file %r: %s"
msgstr ""

#: sphinx/builders/_epub_base.py:433
#, python-format
msgid "cannot write image file %r: %s"
msgstr ""

#: sphinx/builders/_epub_base.py:443
msgid "Pillow not found - copying image files"
msgstr ""

#: sphinx/builders/_epub_base.py:469
msgid "writing mimetype file..."
msgstr ""

#: sphinx/builders/_epub_base.py:474
msgid "writing META-INF/container.xml file..."
msgstr ""

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr ""

#: sphinx/builders/_epub_base.py:531
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr ""

#: sphinx/builders/_epub_base.py:678
msgid "writing toc.ncx file..."
msgstr ""

#: sphinx/builders/_epub_base.py:703
#, python-format
msgid "writing %s file..."
msgstr ""

#: sphinx/builders/changes.py:30
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr ""

#: sphinx/builders/changes.py:56
#, python-format
msgid "no changes in version %s."
msgstr ""

#: sphinx/builders/changes.py:58
msgid "writing summary file..."
msgstr ""

#: sphinx/builders/changes.py:73
msgid "Builtins"
msgstr ""

#: sphinx/builders/changes.py:75
msgid "Module level"
msgstr ""

#: sphinx/builders/changes.py:118
msgid "copying source files..."
msgstr ""

#: sphinx/builders/changes.py:125
#, python-format
msgid "could not read %r for changelog creation"
msgstr ""

#: sphinx/builders/dummy.py:18
msgid "The dummy builder generates no files."
msgstr ""

#: sphinx/builders/epub3.py:79
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr ""

#: sphinx/builders/epub3.py:183
msgid "writing nav.xhtml file..."
msgstr ""

#: sphinx/builders/epub3.py:209
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:213
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:216
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:220
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:223
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:226
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:229
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:232
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:236
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:239
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:253 sphinx/builders/html/__init__.py:1189
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr ""

#: sphinx/builders/gettext.py:215
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr ""

#: sphinx/builders/gettext.py:237
#, python-format
msgid "targets for %d template files"
msgstr ""

#: sphinx/builders/gettext.py:241
msgid "reading templates... "
msgstr ""

#: sphinx/builders/gettext.py:275
msgid "writing message catalogs... "
msgstr ""

#: sphinx/builders/linkcheck.py:60
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr ""

#: sphinx/builders/linkcheck.py:109
#, python-format
msgid "broken link: %s (%s)"
msgstr ""

#: sphinx/builders/linkcheck.py:606
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: sphinx/builders/manpage.py:35
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr ""

#: sphinx/builders/manpage.py:42
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr ""

#: sphinx/builders/latex/__init__.py:309 sphinx/builders/manpage.py:51
#: sphinx/builders/singlehtml.py:163 sphinx/builders/texinfo.py:110
msgid "writing"
msgstr ""

#: sphinx/builders/manpage.py:66
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr ""

#: sphinx/builders/singlehtml.py:32
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr ""

#: sphinx/builders/singlehtml.py:158
msgid "assembling single document"
msgstr ""

#: sphinx/builders/singlehtml.py:176
msgid "writing additional files"
msgstr ""

#: sphinx/builders/texinfo.py:46
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr ""

#: sphinx/builders/texinfo.py:48
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr ""

#: sphinx/builders/texinfo.py:75
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr ""

#: sphinx/builders/texinfo.py:83
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr ""

#: sphinx/builders/latex/__init__.py:291 sphinx/builders/texinfo.py:106
#, python-format
msgid "processing %s"
msgstr ""

#: sphinx/builders/latex/__init__.py:364 sphinx/builders/texinfo.py:159
msgid "resolving references..."
msgstr ""

#: sphinx/builders/latex/__init__.py:374 sphinx/builders/texinfo.py:168
msgid " (in "
msgstr ""

#: sphinx/builders/texinfo.py:198
msgid "copying Texinfo support files"
msgstr ""

#: sphinx/builders/texinfo.py:202
#, python-format
msgid "error writing file Makefile: %s"
msgstr ""

#: sphinx/builders/text.py:29
#, python-format
msgid "The text files are in %(outdir)s."
msgstr ""

#: sphinx/builders/html/__init__.py:1140 sphinx/builders/text.py:76
#: sphinx/builders/xml.py:94
#, python-format
msgid "error writing file %s: %s"
msgstr ""

#: sphinx/builders/xml.py:34
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr ""

#: sphinx/builders/xml.py:106
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr ""

#: sphinx/builders/html/__init__.py:122
#, python-format
msgid "build info file is broken: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:159
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr ""

#: sphinx/builders/html/__init__.py:385
#, python-format
msgid "Failed to read build info file: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:478 sphinx/builders/latex/__init__.py:187
#: sphinx/transforms/__init__.py:117 sphinx/writers/manpage.py:100
#: sphinx/writers/texinfo.py:225
#, python-format
msgid "%b %d, %Y"
msgstr ""

#: sphinx/builders/html/__init__.py:497 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr ""

#: sphinx/builders/html/__init__.py:497
msgid "index"
msgstr ""

#: sphinx/builders/html/__init__.py:569
msgid "next"
msgstr "அடுத்த"

#: sphinx/builders/html/__init__.py:578
msgid "previous"
msgstr ""

#: sphinx/builders/html/__init__.py:674
msgid "generating indices"
msgstr ""

#: sphinx/builders/html/__init__.py:689
msgid "writing additional pages"
msgstr ""

#: sphinx/builders/html/__init__.py:768
msgid "copying downloadable files... "
msgstr ""

#: sphinx/builders/html/__init__.py:776
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr ""

#: sphinx/builders/html/__init__.py:809 sphinx/builders/html/__init__.py:821
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:842
msgid "copying static files"
msgstr ""

#: sphinx/builders/html/__init__.py:858
#, python-format
msgid "cannot copy static file %r"
msgstr ""

#: sphinx/builders/html/__init__.py:863
msgid "copying extra files"
msgstr ""

#: sphinx/builders/html/__init__.py:869
#, python-format
msgid "cannot copy extra file %r"
msgstr ""

#: sphinx/builders/html/__init__.py:876
#, python-format
msgid "Failed to write build info file: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:925
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr ""

#: sphinx/builders/html/__init__.py:986
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr ""

#: sphinx/builders/html/__init__.py:1123
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr ""

#: sphinx/builders/html/__init__.py:1128
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:1156
msgid "dumping object inventory"
msgstr ""

#: sphinx/builders/html/__init__.py:1164
#, python-format
msgid "dumping search index in %s"
msgstr ""

#: sphinx/builders/html/__init__.py:1212
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr ""

#: sphinx/builders/html/__init__.py:1240
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr ""

#: sphinx/builders/html/__init__.py:1243
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr ""

#: sphinx/builders/html/__init__.py:1251
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr ""

#: sphinx/builders/html/__init__.py:1255
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr ""

#: sphinx/builders/html/__init__.py:1264
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr ""

#: sphinx/builders/html/__init__.py:1268
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr ""

#: sphinx/builders/html/__init__.py:1277 sphinx/builders/latex/__init__.py:437
#, python-format
msgid "logo file %r does not exist"
msgstr ""

#: sphinx/builders/html/__init__.py:1286
#, python-format
msgid "favicon file %r does not exist"
msgstr ""

#: sphinx/builders/html/__init__.py:1293
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: sphinx/builders/html/__init__.py:1308
#, python-format
msgid "%s %s documentation"
msgstr ""

#: sphinx/builders/latex/__init__.py:113
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr ""

#: sphinx/builders/latex/__init__.py:115
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr ""

#: sphinx/builders/latex/__init__.py:150
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr ""

#: sphinx/builders/latex/__init__.py:158
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr ""

#: sphinx/builders/latex/__init__.py:194 sphinx/domains/std.py:557
#: sphinx/domains/std.py:569 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:493
msgid "Index"
msgstr ""

#: sphinx/builders/latex/__init__.py:197 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr ""

#: sphinx/builders/latex/__init__.py:211 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr ""

#: sphinx/builders/latex/__init__.py:387
msgid "copying TeX support files"
msgstr ""

#: sphinx/builders/latex/__init__.py:403
msgid "copying TeX support files..."
msgstr ""

#: sphinx/builders/latex/__init__.py:416
msgid "copying additional files"
msgstr ""

#: sphinx/builders/latex/__init__.py:459
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr ""

#: sphinx/builders/latex/__init__.py:467
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr ""

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr ""

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr ""

#: sphinx/builders/latex/transforms.py:117
msgid "Failed to get a docname!"
msgstr ""

#: sphinx/builders/latex/transforms.py:118
msgid "Failed to get a docname for source {source!r}!"
msgstr ""

#: sphinx/builders/latex/transforms.py:479
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr ""

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr ""

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr ""

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr ""

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr ""

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr ""

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr ""

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr ""

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr ""

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr ""

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:477
#: sphinx/ext/apidoc.py:319 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr ""

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr ""

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr ""

#: sphinx/cmd/build.py:143
msgid "a list of specific files to rebuild. Ignored if -a is specified"
msgstr ""

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr ""

#: sphinx/cmd/build.py:149
msgid "builder to use (default: html)"
msgstr ""

#: sphinx/cmd/build.py:151
msgid "write all files (default: only write new and changed files)"
msgstr ""

#: sphinx/cmd/build.py:154
msgid "don't use a saved environment, always read all files"
msgstr ""

#: sphinx/cmd/build.py:157
msgid ""
"path for the cached environment and doctree files (default: "
"OUTPUTDIR/.doctrees)"
msgstr ""

#: sphinx/cmd/build.py:161
msgid ""
"build in parallel with N processes where possible (special value \"auto\" "
"will set N to cpu-count)"
msgstr ""

#: sphinx/cmd/build.py:165
msgid ""
"path where configuration file (conf.py) is located (default: same as "
"SOURCEDIR)"
msgstr ""

#: sphinx/cmd/build.py:168
msgid "use no config file at all, only -D options"
msgstr ""

#: sphinx/cmd/build.py:171
msgid "override a setting in configuration file"
msgstr ""

#: sphinx/cmd/build.py:174
msgid "pass a value into HTML templates"
msgstr ""

#: sphinx/cmd/build.py:177
msgid "define tag: include \"only\" blocks with TAG"
msgstr ""

#: sphinx/cmd/build.py:179
msgid "nit-picky mode, warn about all missing references"
msgstr ""

#: sphinx/cmd/build.py:182
msgid "console output options"
msgstr ""

#: sphinx/cmd/build.py:184
msgid "increase verbosity (can be repeated)"
msgstr ""

#: sphinx/cmd/build.py:186 sphinx/ext/apidoc.py:342
msgid "no output on stdout, just warnings on stderr"
msgstr ""

#: sphinx/cmd/build.py:188
msgid "no output at all, not even warnings"
msgstr ""

#: sphinx/cmd/build.py:191
msgid "do emit colored output (default: auto-detect)"
msgstr ""

#: sphinx/cmd/build.py:194
msgid "do not emit colored output (default: auto-detect)"
msgstr ""

#: sphinx/cmd/build.py:197
msgid "write warnings (and errors) to given file"
msgstr ""

#: sphinx/cmd/build.py:199
msgid "turn warnings into errors"
msgstr ""

#: sphinx/cmd/build.py:201
msgid "with -W, keep going when getting warnings"
msgstr ""

#: sphinx/cmd/build.py:203
msgid "show full traceback on exception"
msgstr ""

#: sphinx/cmd/build.py:205
msgid "run Pdb on exception"
msgstr ""

#: sphinx/cmd/build.py:229
msgid "cannot combine -a option and filenames"
msgstr ""

#: sphinx/cmd/build.py:250
#, python-format
msgid "cannot open warning file %r: %s"
msgstr ""

#: sphinx/cmd/build.py:264
msgid "-D option argument must be in the form name=value"
msgstr ""

#: sphinx/cmd/build.py:271
msgid "-A option argument must be in the form name=value"
msgstr ""

#: sphinx/cmd/quickstart.py:48
msgid "automatically insert docstrings from modules"
msgstr ""

#: sphinx/cmd/quickstart.py:49
msgid "automatically test code snippets in doctest blocks"
msgstr ""

#: sphinx/cmd/quickstart.py:50
msgid "link between Sphinx documentation of different projects"
msgstr ""

#: sphinx/cmd/quickstart.py:51
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr ""

#: sphinx/cmd/quickstart.py:52
msgid "checks for documentation coverage"
msgstr ""

#: sphinx/cmd/quickstart.py:53
msgid "include math, rendered as PNG or SVG images"
msgstr ""

#: sphinx/cmd/quickstart.py:54
msgid "include math, rendered in the browser by MathJax"
msgstr ""

#: sphinx/cmd/quickstart.py:55
msgid "conditional inclusion of content based on config values"
msgstr ""

#: sphinx/cmd/quickstart.py:56
msgid "include links to the source code of documented Python objects"
msgstr ""

#: sphinx/cmd/quickstart.py:57
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr ""

#: sphinx/cmd/quickstart.py:99
msgid "Please enter a valid path name."
msgstr ""

#: sphinx/cmd/quickstart.py:115
msgid "Please enter some text."
msgstr ""

#: sphinx/cmd/quickstart.py:122
#, python-format
msgid "Please enter one of %s."
msgstr ""

#: sphinx/cmd/quickstart.py:129
msgid "Please enter either 'y' or 'n'."
msgstr ""

#: sphinx/cmd/quickstart.py:135
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr ""

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr ""

#: sphinx/cmd/quickstart.py:217
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr ""

#: sphinx/cmd/quickstart.py:222
#, python-format
msgid "Selected root path: %s"
msgstr ""

#: sphinx/cmd/quickstart.py:225
msgid "Enter the root path for documentation."
msgstr ""

#: sphinx/cmd/quickstart.py:226
msgid "Root path for the documentation"
msgstr ""

#: sphinx/cmd/quickstart.py:231
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr ""

#: sphinx/cmd/quickstart.py:233
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr ""

#: sphinx/cmd/quickstart.py:235
msgid "Please enter a new root path (or just Enter to exit)"
msgstr ""

#: sphinx/cmd/quickstart.py:242
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr ""

#: sphinx/cmd/quickstart.py:245
msgid "Separate source and build directories (y/n)"
msgstr ""

#: sphinx/cmd/quickstart.py:249
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr ""

#: sphinx/cmd/quickstart.py:252
msgid "Name prefix for templates and static dir"
msgstr ""

#: sphinx/cmd/quickstart.py:256
msgid ""
"The project name will occur in several places in the built documentation."
msgstr ""

#: sphinx/cmd/quickstart.py:257
msgid "Project name"
msgstr ""

#: sphinx/cmd/quickstart.py:259
msgid "Author name(s)"
msgstr ""

#: sphinx/cmd/quickstart.py:263
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: sphinx/cmd/quickstart.py:268
msgid "Project version"
msgstr ""

#: sphinx/cmd/quickstart.py:270
msgid "Project release"
msgstr ""

#: sphinx/cmd/quickstart.py:274
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr ""

#: sphinx/cmd/quickstart.py:282
msgid "Project language"
msgstr ""

#: sphinx/cmd/quickstart.py:288
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: sphinx/cmd/quickstart.py:290
msgid "Source file suffix"
msgstr ""

#: sphinx/cmd/quickstart.py:294
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: sphinx/cmd/quickstart.py:298
msgid "Name of your master document (without suffix)"
msgstr ""

#: sphinx/cmd/quickstart.py:303
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr ""

#: sphinx/cmd/quickstart.py:305
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr ""

#: sphinx/cmd/quickstart.py:307
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr ""

#: sphinx/cmd/quickstart.py:311
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr ""

#: sphinx/cmd/quickstart.py:319
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr ""

#: sphinx/cmd/quickstart.py:325
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr ""

#: sphinx/cmd/quickstart.py:328
msgid "Create Makefile? (y/n)"
msgstr ""

#: sphinx/cmd/quickstart.py:331
msgid "Create Windows command file? (y/n)"
msgstr ""

#: sphinx/cmd/quickstart.py:375 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr ""

#: sphinx/cmd/quickstart.py:380 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr ""

#: sphinx/cmd/quickstart.py:422
msgid "Finished: An initial directory structure has been created."
msgstr ""

#: sphinx/cmd/quickstart.py:424
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr ""

#: sphinx/cmd/quickstart.py:427
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr ""

#: sphinx/cmd/quickstart.py:430
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr ""

#: sphinx/cmd/quickstart.py:432
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr ""

#: sphinx/cmd/quickstart.py:467
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr ""

#: sphinx/cmd/quickstart.py:482
msgid "quiet mode"
msgstr ""

#: sphinx/cmd/quickstart.py:487
msgid "project root"
msgstr ""

#: sphinx/cmd/quickstart.py:489
msgid "Structure options"
msgstr ""

#: sphinx/cmd/quickstart.py:491
msgid "if specified, separate source and build dirs"
msgstr ""

#: sphinx/cmd/quickstart.py:493
msgid "if specified, create build dir under source dir"
msgstr ""

#: sphinx/cmd/quickstart.py:495
msgid "replacement for dot in _templates etc."
msgstr ""

#: sphinx/cmd/quickstart.py:497
msgid "Project basic options"
msgstr ""

#: sphinx/cmd/quickstart.py:499
msgid "project name"
msgstr ""

#: sphinx/cmd/quickstart.py:501
msgid "author names"
msgstr ""

#: sphinx/cmd/quickstart.py:503
msgid "version of project"
msgstr ""

#: sphinx/cmd/quickstart.py:505
msgid "release of project"
msgstr ""

#: sphinx/cmd/quickstart.py:507
msgid "document language"
msgstr ""

#: sphinx/cmd/quickstart.py:509
msgid "source file suffix"
msgstr ""

#: sphinx/cmd/quickstart.py:511
msgid "master document name"
msgstr ""

#: sphinx/cmd/quickstart.py:513
msgid "use epub"
msgstr ""

#: sphinx/cmd/quickstart.py:515
msgid "Extension options"
msgstr ""

#: sphinx/cmd/quickstart.py:519 sphinx/ext/apidoc.py:402
#, python-format
msgid "enable %s extension"
msgstr ""

#: sphinx/cmd/quickstart.py:521 sphinx/ext/apidoc.py:398
msgid "enable arbitrary extensions"
msgstr ""

#: sphinx/cmd/quickstart.py:523
msgid "Makefile and Batchfile creation"
msgstr ""

#: sphinx/cmd/quickstart.py:525
msgid "create makefile"
msgstr ""

#: sphinx/cmd/quickstart.py:527
msgid "do not create makefile"
msgstr ""

#: sphinx/cmd/quickstart.py:529
msgid "create batchfile"
msgstr ""

#: sphinx/cmd/quickstart.py:532
msgid "do not create batchfile"
msgstr ""

#: sphinx/cmd/quickstart.py:535
msgid "use make-mode for Makefile/make.bat"
msgstr ""

#: sphinx/cmd/quickstart.py:538
msgid "do not use make-mode for Makefile/make.bat"
msgstr ""

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:404
msgid "Project templating"
msgstr ""

#: sphinx/cmd/quickstart.py:543 sphinx/ext/apidoc.py:407
msgid "template directory for template files"
msgstr ""

#: sphinx/cmd/quickstart.py:546
msgid "define a template variable"
msgstr ""

#: sphinx/cmd/quickstart.py:579
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr ""

#: sphinx/cmd/quickstart.py:593
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr ""

#: sphinx/cmd/quickstart.py:595
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr ""

#: sphinx/cmd/quickstart.py:610
#, python-format
msgid "Invalid template variable: %s"
msgstr ""

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr ""

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr ""

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr ""

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr ""

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr ""

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr ""

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr ""

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr ""

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr ""

#: sphinx/directives/other.py:116
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: sphinx/directives/other.py:142 sphinx/environment/adapters/toctree.py:323
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr ""

#: sphinx/directives/other.py:145 sphinx/environment/adapters/toctree.py:327
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr ""

#: sphinx/directives/other.py:156
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr ""

#: sphinx/directives/other.py:188
msgid "Section author: "
msgstr ""

#: sphinx/directives/other.py:190
msgid "Module author: "
msgstr ""

#: sphinx/directives/other.py:192
msgid "Code author: "
msgstr ""

#: sphinx/directives/other.py:194
msgid "Author: "
msgstr ""

#: sphinx/directives/other.py:266
msgid ".. acks content is not a list"
msgstr ""

#: sphinx/directives/other.py:291
msgid ".. hlist content is not a list"
msgstr ""

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr ""

#: sphinx/domains/c.py:2043 sphinx/domains/c.py:3318
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: sphinx/domains/c.py:3257
#, python-format
msgid "%s (C %s)"
msgstr ""

#: sphinx/domains/c.py:3356 sphinx/domains/cpp.py:7496
#: sphinx/domains/python.py:682 sphinx/ext/napoleon/docstring.py:760
msgid "Parameters"
msgstr ""

#: sphinx/domains/c.py:3359 sphinx/domains/cpp.py:7502
msgid "Return values"
msgstr ""

#: sphinx/domains/c.py:3362 sphinx/domains/cpp.py:7505
#: sphinx/domains/javascript.py:259 sphinx/domains/python.py:694
msgid "Returns"
msgstr ""

#: sphinx/domains/c.py:3364 sphinx/domains/javascript.py:261
#: sphinx/domains/python.py:696
msgid "Return type"
msgstr ""

#: sphinx/domains/c.py:3730 sphinx/domains/cpp.py:7909
msgid "member"
msgstr ""

#: sphinx/domains/c.py:3731
msgid "variable"
msgstr ""

#: sphinx/domains/c.py:3732 sphinx/domains/cpp.py:7908
#: sphinx/domains/javascript.py:365 sphinx/domains/python.py:1454
msgid "function"
msgstr ""

#: sphinx/domains/c.py:3733
msgid "macro"
msgstr ""

#: sphinx/domains/c.py:3734
msgid "struct"
msgstr ""

#: sphinx/domains/c.py:3735 sphinx/domains/cpp.py:7907
msgid "union"
msgstr ""

#: sphinx/domains/c.py:3736 sphinx/domains/cpp.py:7912
msgid "enum"
msgstr ""

#: sphinx/domains/c.py:3737 sphinx/domains/cpp.py:7913
msgid "enumerator"
msgstr ""

#: sphinx/domains/c.py:3738 sphinx/domains/cpp.py:7910
msgid "type"
msgstr ""

#: sphinx/domains/c.py:3740 sphinx/domains/cpp.py:7915
msgid "function parameter"
msgstr ""

#: sphinx/domains/changeset.py:23
#, python-format
msgid "New in version %s"
msgstr ""

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr ""

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr ""

#: sphinx/domains/citation.py:70
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr ""

#: sphinx/domains/citation.py:81
#, python-format
msgid "Citation [%s] is not referenced."
msgstr ""

#: sphinx/domains/cpp.py:4929 sphinx/domains/cpp.py:7423
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: sphinx/domains/cpp.py:7218
msgid "Template Parameters"
msgstr ""

#: sphinx/domains/cpp.py:7340
#, python-format
msgid "%s (C++ %s)"
msgstr ""

#: sphinx/domains/cpp.py:7499 sphinx/domains/javascript.py:256
msgid "Throws"
msgstr ""

#: sphinx/domains/cpp.py:7906 sphinx/domains/javascript.py:367
#: sphinx/domains/python.py:1456
msgid "class"
msgstr ""

#: sphinx/domains/cpp.py:7911
msgid "concept"
msgstr ""

#: sphinx/domains/cpp.py:7916
msgid "template parameter"
msgstr ""

#: sphinx/domains/javascript.py:164
#, python-format
msgid "%s() (built-in function)"
msgstr ""

#: sphinx/domains/javascript.py:165 sphinx/domains/python.py:1121
#, python-format
msgid "%s() (%s method)"
msgstr ""

#: sphinx/domains/javascript.py:167
#, python-format
msgid "%s() (class)"
msgstr ""

#: sphinx/domains/javascript.py:169
#, python-format
msgid "%s (global variable or constant)"
msgstr ""

#: sphinx/domains/javascript.py:171 sphinx/domains/python.py:1206
#, python-format
msgid "%s (%s attribute)"
msgstr ""

#: sphinx/domains/javascript.py:253
msgid "Arguments"
msgstr ""

#: sphinx/domains/javascript.py:329
#, python-format
msgid "%s (module)"
msgstr ""

#: sphinx/domains/javascript.py:366 sphinx/domains/python.py:1458
msgid "method"
msgstr ""

#: sphinx/domains/javascript.py:368 sphinx/domains/python.py:1455
msgid "data"
msgstr ""

#: sphinx/domains/javascript.py:369 sphinx/domains/python.py:1461
msgid "attribute"
msgstr ""

#: sphinx/domains/javascript.py:370 sphinx/domains/python.py:1463
msgid "module"
msgstr ""

#: sphinx/domains/javascript.py:401
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr ""

#: sphinx/domains/math.py:61
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr ""

#: sphinx/domains/math.py:116 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr ""

#: sphinx/domains/python.py:687
msgid "Variables"
msgstr ""

#: sphinx/domains/python.py:691
msgid "Raises"
msgstr ""

#: sphinx/domains/python.py:975 sphinx/domains/python.py:1112
#, python-format
msgid "%s() (in module %s)"
msgstr ""

#: sphinx/domains/python.py:1035 sphinx/domains/python.py:1202
#: sphinx/domains/python.py:1253
#, python-format
msgid "%s (in module %s)"
msgstr ""

#: sphinx/domains/python.py:1037
#, python-format
msgid "%s (built-in variable)"
msgstr ""

#: sphinx/domains/python.py:1062
#, python-format
msgid "%s (built-in class)"
msgstr ""

#: sphinx/domains/python.py:1063
#, python-format
msgid "%s (class in %s)"
msgstr ""

#: sphinx/domains/python.py:1117
#, python-format
msgid "%s() (%s class method)"
msgstr ""

#: sphinx/domains/python.py:1119
#, python-format
msgid "%s() (%s static method)"
msgstr ""

#: sphinx/domains/python.py:1257
#, python-format
msgid "%s (%s property)"
msgstr ""

#: sphinx/domains/python.py:1383
msgid "Python Module Index"
msgstr ""

#: sphinx/domains/python.py:1384
msgid "modules"
msgstr ""

#: sphinx/domains/python.py:1433
msgid "Deprecated"
msgstr ""

#: sphinx/domains/python.py:1457
msgid "exception"
msgstr ""

#: sphinx/domains/python.py:1459
msgid "class method"
msgstr ""

#: sphinx/domains/python.py:1460
msgid "static method"
msgstr ""

#: sphinx/domains/python.py:1462
msgid "property"
msgstr ""

#: sphinx/domains/python.py:1520
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: sphinx/domains/python.py:1640
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr ""

#: sphinx/domains/python.py:1701
msgid " (deprecated)"
msgstr ""

#: sphinx/domains/rst.py:125 sphinx/domains/rst.py:181
#, python-format
msgid "%s (directive)"
msgstr ""

#: sphinx/domains/rst.py:182 sphinx/domains/rst.py:186
#, python-format
msgid ":%s: (directive option)"
msgstr ""

#: sphinx/domains/rst.py:209
#, python-format
msgid "%s (role)"
msgstr ""

#: sphinx/domains/rst.py:218
msgid "directive"
msgstr ""

#: sphinx/domains/rst.py:219
msgid "directive-option"
msgstr ""

#: sphinx/domains/rst.py:220
msgid "role"
msgstr ""

#: sphinx/domains/rst.py:242
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr ""

#: sphinx/domains/std.py:79 sphinx/domains/std.py:96
#, python-format
msgid "environment variable; %s"
msgstr ""

#: sphinx/domains/std.py:155
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr ""

#: sphinx/domains/std.py:226
#, python-format
msgid "%s command line option"
msgstr ""

#: sphinx/domains/std.py:228
msgid "command line option"
msgstr ""

#: sphinx/domains/std.py:346
msgid "glossary term must be preceded by empty line"
msgstr ""

#: sphinx/domains/std.py:354
msgid "glossary terms must not be separated by empty lines"
msgstr ""

#: sphinx/domains/std.py:360 sphinx/domains/std.py:373
msgid "glossary seems to be misformatted, check indentation"
msgstr ""

#: sphinx/domains/std.py:516
msgid "glossary term"
msgstr ""

#: sphinx/domains/std.py:517
msgid "grammar token"
msgstr ""

#: sphinx/domains/std.py:518
msgid "reference label"
msgstr ""

#: sphinx/domains/std.py:520
msgid "environment variable"
msgstr ""

#: sphinx/domains/std.py:521
msgid "program option"
msgstr ""

#: sphinx/domains/std.py:522
msgid "document"
msgstr ""

#: sphinx/domains/std.py:558 sphinx/domains/std.py:570
msgid "Module Index"
msgstr ""

#: sphinx/domains/std.py:559 sphinx/domains/std.py:571
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr ""

#: sphinx/domains/std.py:614 sphinx/domains/std.py:720
#: sphinx/ext/autosectionlabel.py:52
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr ""

#: sphinx/domains/std.py:633
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr ""

#: sphinx/domains/std.py:839
msgid "numfig is disabled. :numref: is ignored."
msgstr ""

#: sphinx/domains/std.py:847
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: sphinx/domains/std.py:859
#, python-format
msgid "the link has no caption: %s"
msgstr ""

#: sphinx/domains/std.py:873
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr ""

#: sphinx/domains/std.py:876
#, python-format
msgid "invalid numfig_format: %s"
msgstr ""

#: sphinx/domains/std.py:1106
#, python-format
msgid "undefined label: %r"
msgstr ""

#: sphinx/domains/std.py:1108
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr ""

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr ""

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr ""

#: sphinx/environment/__init__.py:276
msgid "build environment version not current"
msgstr ""

#: sphinx/environment/__init__.py:278
msgid "source directory has changed"
msgstr ""

#: sphinx/environment/__init__.py:357
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr ""

#: sphinx/environment/__init__.py:456
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr ""

#: sphinx/environment/__init__.py:593
#, python-format
msgid "Domain %r is not registered"
msgstr ""

#: sphinx/environment/__init__.py:727
msgid "document isn't included in any toctree"
msgstr ""

#: sphinx/environment/__init__.py:764
msgid "self referenced toctree found. Ignored."
msgstr ""

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr ""

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr ""

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr ""

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr ""

#: sphinx/environment/adapters/toctree.py:296
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr ""

#: sphinx/environment/adapters/toctree.py:316
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr ""

#: sphinx/environment/adapters/toctree.py:325
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: sphinx/environment/collectors/asset.py:88
#, python-format
msgid "image file not readable: %s"
msgstr ""

#: sphinx/environment/collectors/asset.py:107
#, python-format
msgid "image file %s not readable: %s"
msgstr ""

#: sphinx/environment/collectors/asset.py:133
#, python-format
msgid "download file not readable: %s"
msgstr ""

#: sphinx/environment/collectors/toctree.py:224
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr ""

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr ""

#: sphinx/ext/apidoc.py:320
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr ""

#: sphinx/ext/apidoc.py:333
msgid "path to module to document"
msgstr ""

#: sphinx/ext/apidoc.py:335
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr ""

#: sphinx/ext/apidoc.py:340
msgid "directory to place all output"
msgstr ""

#: sphinx/ext/apidoc.py:345
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr ""

#: sphinx/ext/apidoc.py:348
msgid "overwrite existing files"
msgstr ""

#: sphinx/ext/apidoc.py:351
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr ""

#: sphinx/ext/apidoc.py:354
msgid "run the script without creating files"
msgstr ""

#: sphinx/ext/apidoc.py:357
msgid "put documentation for each module on its own page"
msgstr ""

#: sphinx/ext/apidoc.py:360
msgid "include \"_private\" modules"
msgstr ""

#: sphinx/ext/apidoc.py:362
msgid "filename of table of contents (default: modules)"
msgstr ""

#: sphinx/ext/apidoc.py:364
msgid "don't create a table of contents file"
msgstr ""

#: sphinx/ext/apidoc.py:367
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr ""

#: sphinx/ext/apidoc.py:372
msgid "put module documentation before submodule documentation"
msgstr ""

#: sphinx/ext/apidoc.py:376
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr ""

#: sphinx/ext/apidoc.py:380
msgid "file suffix (default: rst)"
msgstr ""

#: sphinx/ext/apidoc.py:382
msgid "generate a full project with sphinx-quickstart"
msgstr ""

#: sphinx/ext/apidoc.py:385
msgid "append module_path to sys.path, used when --full is given"
msgstr ""

#: sphinx/ext/apidoc.py:387
msgid "project name (default: root module name)"
msgstr ""

#: sphinx/ext/apidoc.py:389
msgid "project author(s), used when --full is given"
msgstr ""

#: sphinx/ext/apidoc.py:391
msgid "project version, used when --full is given"
msgstr ""

#: sphinx/ext/apidoc.py:393
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr ""

#: sphinx/ext/apidoc.py:396
msgid "extension options"
msgstr ""

#: sphinx/ext/apidoc.py:429
#, python-format
msgid "%s is not a directory."
msgstr ""

#: sphinx/ext/autosectionlabel.py:48
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: sphinx/ext/coverage.py:45
#, python-format
msgid "invalid regex %r in %s"
msgstr ""

#: sphinx/ext/coverage.py:73
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr ""

#: sphinx/ext/coverage.py:87
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr ""

#: sphinx/ext/coverage.py:155
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: sphinx/ext/coverage.py:187
#, python-format
msgid "module %s could not be imported: %s"
msgstr ""

#: sphinx/ext/coverage.py:334
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:350
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:363
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr ""

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr ""

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr ""

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr ""

#: sphinx/ext/doctest.py:280
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr ""

#: sphinx/ext/doctest.py:431
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr ""

#: sphinx/ext/doctest.py:521
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr ""

#: sphinx/ext/duration.py:76
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: sphinx/ext/graphviz.py:133
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr ""

#: sphinx/ext/graphviz.py:143
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr ""

#: sphinx/ext/graphviz.py:150
msgid "Ignoring \"graphviz\" directive without content."
msgstr ""

#: sphinx/ext/graphviz.py:259
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: sphinx/ext/graphviz.py:294
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr ""

#: sphinx/ext/graphviz.py:301
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/graphviz.py:304
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/graphviz.py:320
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr ""

#: sphinx/ext/graphviz.py:324 sphinx/ext/graphviz.py:377
#: sphinx/ext/graphviz.py:414
#, python-format
msgid "dot code %r: %s"
msgstr ""

#: sphinx/ext/graphviz.py:427 sphinx/ext/graphviz.py:435
#, python-format
msgid "[graph: %s]"
msgstr ""

#: sphinx/ext/graphviz.py:429 sphinx/ext/graphviz.py:437
msgid "[graph]"
msgstr ""

#: sphinx/ext/imgconverter.py:38
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: sphinx/ext/imgconverter.py:47 sphinx/ext/imgconverter.py:71
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/imgconverter.py:66
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr ""

#: sphinx/ext/imgmath.py:157
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr ""

#: sphinx/ext/imgmath.py:172
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr ""

#: sphinx/ext/imgmath.py:326
#, python-format
msgid "display latex %r: %s"
msgstr ""

#: sphinx/ext/imgmath.py:360
#, python-format
msgid "inline latex %r: %s"
msgstr ""

#: sphinx/ext/imgmath.py:367 sphinx/ext/mathjax.py:52
msgid "Link to this equation"
msgstr ""

#: sphinx/ext/intersphinx.py:194
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr ""

#: sphinx/ext/intersphinx.py:229
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr ""

#: sphinx/ext/intersphinx.py:243
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr ""

#: sphinx/ext/intersphinx.py:249
msgid "failed to reach any of the inventories with the following issues:"
msgstr ""

#: sphinx/ext/intersphinx.py:302
#, python-format
msgid "(in %s v%s)"
msgstr ""

#: sphinx/ext/intersphinx.py:304
#, python-format
msgid "(in %s)"
msgstr ""

#: sphinx/ext/intersphinx.py:536
#, python-format
msgid "inventory for external cross-reference not found: %s"
msgstr ""

#: sphinx/ext/intersphinx.py:542
#, python-format
msgid "role for external cross-reference not found: %s"
msgstr ""

#: sphinx/ext/intersphinx.py:633
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: sphinx/ext/intersphinx.py:658
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr ""

#: sphinx/ext/intersphinx.py:680
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr ""

#: sphinx/ext/linkcode.py:68 sphinx/ext/viewcode.py:198
msgid "[source]"
msgstr ""

#: sphinx/ext/todo.py:67
msgid "Todo"
msgstr ""

#: sphinx/ext/todo.py:100
#, python-format
msgid "TODO entry found: %s"
msgstr ""

#: sphinx/ext/todo.py:158
msgid "<<original entry>>"
msgstr ""

#: sphinx/ext/todo.py:160
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr ""

#: sphinx/ext/todo.py:170
msgid "original entry"
msgstr ""

#: sphinx/ext/viewcode.py:255
msgid "highlighting module code... "
msgstr ""

#: sphinx/ext/viewcode.py:283
msgid "[docs]"
msgstr ""

#: sphinx/ext/viewcode.py:303
msgid "Module code"
msgstr ""

#: sphinx/ext/viewcode.py:309
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr ""

#: sphinx/ext/viewcode.py:335
msgid "Overview: module code"
msgstr ""

#: sphinx/ext/viewcode.py:336
msgid "<h1>All modules for which code is available</h1>"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:127
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:391
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:508
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:777
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:872
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:916
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:935
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:996
msgid "\"::\" in automodule name doesn't make sense"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1003
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1016
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1082
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1298 sphinx/ext/autodoc/__init__.py:1375
#: sphinx/ext/autodoc/__init__.py:2768
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1586
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1713
#, python-format
msgid "Bases: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1727
#, python-format
msgid "missing attribute %s in object %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1826 sphinx/ext/autodoc/__init__.py:1863
#: sphinx/ext/autodoc/__init__.py:1946
#, python-format
msgid "alias of %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1846
#, python-format
msgid "alias of TypeVar(%s)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2167 sphinx/ext/autodoc/__init__.py:2264
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2395
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr ""

#: sphinx/ext/autodoc/preserve_defaults.py:183
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:131
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:134
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:249
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:270
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:323
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:337
#, python-format
msgid "failed to parse name %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:342
#, python-format
msgid "failed to import object %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:798
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:806
msgid ""
"autosummary generats .rst files internally. But your source_suffix does not "
"contain .rst. Skipped."
msgstr ""

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr ""

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr ""

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr ""

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: sphinx/ext/napoleon/__init__.py:336 sphinx/ext/napoleon/docstring.py:726
msgid "Keyword Arguments"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:680
msgid "Example"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:681
msgid "Examples"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:742
msgid "Notes"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:751
msgid "Other Parameters"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:787
msgid "Receives"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:791
msgid "References"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:823
msgid "Warns"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:827
msgid "Yields"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:985
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:992
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:999
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1006
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: sphinx/locale/__init__.py:221
msgid "Attention"
msgstr ""

#: sphinx/locale/__init__.py:222
msgid "Caution"
msgstr ""

#: sphinx/locale/__init__.py:223
msgid "Danger"
msgstr ""

#: sphinx/locale/__init__.py:224
msgid "Error"
msgstr ""

#: sphinx/locale/__init__.py:225
msgid "Hint"
msgstr ""

#: sphinx/locale/__init__.py:226
msgid "Important"
msgstr ""

#: sphinx/locale/__init__.py:227
msgid "Note"
msgstr ""

#: sphinx/locale/__init__.py:228
msgid "See also"
msgstr ""

#: sphinx/locale/__init__.py:229
msgid "Tip"
msgstr ""

#: sphinx/locale/__init__.py:230
msgid "Warning"
msgstr ""

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr ""

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr ""

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr ""

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr ""

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr ""

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr ""

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:22
msgid "Search"
msgstr ""

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr ""

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr ""

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr ""

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr ""

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr ""

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr ""

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr ""

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr ""

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr ""

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr ""

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr ""

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr ""

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr ""

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr ""

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr ""

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr ""

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr ""

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr ""

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr ""

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr ""

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr ""

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr ""

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr ""

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr ""

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr ""

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr ""

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr ""

#: sphinx/themes/basic/search.html:27
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr ""

#: sphinx/themes/basic/search.html:35
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr ""

#: sphinx/themes/basic/search.html:42
msgid "search"
msgstr ""

#: sphinx/themes/basic/search.html:48
#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr ""

#: sphinx/themes/basic/search.html:50
#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr ""

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr ""

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr ""

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr ""

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr ""

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr ""

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr ""

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:217
msgid "Searching"
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:233
msgid "Preparing search..."
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:421
msgid ", in "
msgstr ""

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr ""

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr ""

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr ""

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr ""

#: sphinx/transforms/__init__.py:126
msgid "could not calculate translation progress!"
msgstr ""

#: sphinx/transforms/__init__.py:131
msgid "no translated elements!"
msgstr ""

#: sphinx/transforms/__init__.py:238
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr ""

#: sphinx/transforms/__init__.py:277
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr ""

#: sphinx/transforms/__init__.py:283
msgid "Footnote [#] is not referenced."
msgstr ""

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:272
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr ""

#: sphinx/transforms/i18n.py:287
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/i18n.py:304
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:80
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:108
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:126
#, python-format
msgid "Unknown image format: %s..."
msgstr ""

#: sphinx/util/__init__.py:167
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr ""

#: sphinx/util/display.py:77
msgid "skipped"
msgstr ""

#: sphinx/util/display.py:82
msgid "failed"
msgstr ""

#: sphinx/util/docfields.py:88
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: sphinx/util/docutils.py:311
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr ""

#: sphinx/util/docutils.py:605
#, python-format
msgid "unknown node type: %r"
msgstr ""

#: sphinx/util/i18n.py:63
#, python-format
msgid "reading error: %s, %s"
msgstr ""

#: sphinx/util/i18n.py:70
#, python-format
msgid "writing error: %s, %s"
msgstr ""

#: sphinx/util/i18n.py:94
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: sphinx/util/i18n.py:185
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr ""

#: sphinx/util/nodes.py:378
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: sphinx/util/nodes.py:426
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr ""

#: sphinx/util/nodes.py:627
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr ""

#: sphinx/util/rst.py:70
#, python-format
msgid "default role %s not found"
msgstr ""

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr ""

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr ""

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr ""

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr ""

#: sphinx/writers/html5.py:485 sphinx/writers/html5.py:490
msgid "Link to this heading"
msgstr ""

#: sphinx/writers/html5.py:494
msgid "Link to this table"
msgstr ""

#: sphinx/writers/html5.py:537
msgid "Link to this code"
msgstr ""

#: sphinx/writers/html5.py:539
msgid "Link to this image"
msgstr ""

#: sphinx/writers/html5.py:541
msgid "Link to this toctree"
msgstr ""

#: sphinx/writers/html5.py:679
msgid "Could not obtain image size. :scale: option is ignored."
msgstr ""

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr ""

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr ""

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr ""

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:622
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr ""

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:258
#: sphinx/writers/texinfo.py:637
msgid "Footnotes"
msgstr ""

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr ""

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr ""

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr ""

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:917
#, python-format
msgid "[image: %s]"
msgstr ""

#: sphinx/writers/manpage.py:307 sphinx/writers/text.py:918
msgid "[image]"
msgstr ""

#: sphinx/writers/texinfo.py:1193
msgid "caption not inside a figure."
msgstr ""

#: sphinx/writers/texinfo.py:1280
#, python-format
msgid "unimplemented node type: %r"
msgstr ""
