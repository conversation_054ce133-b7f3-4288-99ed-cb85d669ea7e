Documentation.addTranslations({
    "locale": "sk",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "%(filename)s &#8212; %(docstitle)s",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": ", v ",
        "About these documents": "O dokument\u00e1cii",
        "Automatically generated list of changes in version %(version)s": "Automaticky generovan\u00fd zoznam zmien vo verzii %(version)s",
        "C API changes": "Zmeny API C",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "Zmeny vo verzii %(version)s &#8212; %(docstitle)s",
        "Collapse sidebar": "Zbali\u0165 bo\u010dn\u00fd panel",
        "Complete Table of Contents": "Celkov\u00fd obsah",
        "Contents": "Obsah",
        "Copyright": "Autorsk\u00e9 pr\u00e1vo",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "Vytvoren\u00e9 pomocou <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.",
        "Expand sidebar": "Rozbali\u0165 bo\u010dn\u00fd panel",
        "Full index on one page": "Cel\u00fd index na jednej strane",
        "General Index": "V\u0161eobecn\u00fd index",
        "Global Module Index": "Celkov\u00fd index modulov",
        "Go": "OK",
        "Hide Search Matches": "Skry\u0165 v\u00fdsledky h\u013eadania",
        "Index": "Index",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "Indexov\u00e9 str\u00e1nky po p\u00edsmen\u00e1ch",
        "Indices and tables:": "Indexy a tabu\u013eky",
        "Last updated on %(last_updated)s.": "Naposledy aktualizovan\u00e9 %(last_updated)s.",
        "Library changes": "Zmeny kni\u017enice",
        "Navigation": "Navig\u00e1cia",
        "Next topic": "\u010eal\u0161ia t\u00e9ma",
        "Other changes": "Ostatn\u00e9 zmeny",
        "Overview": "Preh\u013ead",
        "Please activate JavaScript to enable the search\n    functionality.": "Pros\u00edm, na zapnutie funkcie h\u013eadania,aktivujte\nJavaScript .",
        "Preparing search...": "Pr\u00edprava h\u013eadania...",
        "Previous topic": "Predo\u0161l\u00e1 t\u00e9ma",
        "Quick search": "R\u00fdchle h\u013eadanie",
        "Search": "H\u013eada\u0165",
        "Search Page": "Str\u00e1nka h\u013eadania",
        "Search Results": "V\u00fdsledky h\u013eadania",
        "Search finished, found one page matching the search query.": [
            "",
            "",
            "",
            ""
        ],
        "Search within %(docstitle)s": "H\u013eada\u0165 v %(docstitle)s",
        "Searching": "H\u013eadanie",
        "Searching for multiple words only shows matches that contain\n    all words.": "H\u013eadanie viacer\u00fdch slov vracia len zhody, ktor\u00e9 obsahuj\u00fa\n    v\u0161etky slov\u00e1.",
        "Show Source": "Zobrazi\u0165 zdroj",
        "Table of Contents": "Obsah",
        "This Page": "T\u00e1to str\u00e1nka",
        "Welcome! This is": "Vitajte! Toto je",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "V\u00e1\u0161mu h\u013eadaniu nezodpoved\u00e1 \u017eiadny dokument. Pros\u00edm, skontrolujte, \u017ee v\u0161etky zadan\u00e9 slov\u00e1 s\u00fa spr\u00e1vne nap\u00edsan\u00e9 a \u017ee ste zvolili vhodn\u00e9 kateg\u00f3rie.",
        "all functions, classes, terms": "v\u0161etky funkcie, triedy, term\u00edny",
        "can be huge": "m\u00f4\u017ee by\u0165 rozsiahle",
        "last updated": "posledn\u00e1 aktualiz\u00e1cia",
        "lists all sections and subsections": "zoznam v\u0161etk\u00fdch sekci\u00ed a podsekci\u00ed",
        "next chapter": "\u010fal\u0161ia kapitola",
        "previous chapter": "predo\u0161l\u00e1 kapitola",
        "quick access to all modules": "r\u00fdchly pr\u00edstup ku v\u0161etk\u00fdm modulom",
        "search": "h\u013eada\u0165",
        "search this documentation": "h\u013eada\u0165 v tejto dokument\u00e1cii",
        "the documentation for": "dokument\u00e1cia"
    },
    "plural_expr": "(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3)"
});