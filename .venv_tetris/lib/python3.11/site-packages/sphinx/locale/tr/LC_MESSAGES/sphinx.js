Documentation.addTranslations({
    "locale": "tr",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "%(filename)s &#8212; %(docstitle)s",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": ", \u015funun i\u00e7inde:",
        "About these documents": "Bu belgeler hakk\u0131nda",
        "Automatically generated list of changes in version %(version)s": "%(version)s s\u00fcr\u00fcm\u00fcndeki de\u011fi\u015fikliklerin otomatik olarak \u00fcretilmi\u015f listesi",
        "C API changes": "C API'sindeki de\u011fi\u015fiklikler",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "S\u00fcr\u00fcm %(version)s &#8212; %(docstitle)s i\u00e7indeki De\u011fi\u015fiklikler",
        "Collapse sidebar": "Yan \u00e7ubu\u011fu daralt",
        "Complete Table of Contents": "Tam \u0130\u00e7indekiler",
        "Contents": "\u0130\u00e7indekiler",
        "Copyright": "Telif hakk\u0131",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "Yan \u00e7ubu\u011fu geni\u015flet",
        "Full index on one page": "Tek sayfada tam dizin",
        "General Index": "Genel Dizin",
        "Global Module Index": "Genel Mod\u00fcl Dizini",
        "Go": "Git",
        "Hide Search Matches": "Arama E\u015fle\u015fmelerini Gizle",
        "Index": "Dizin",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "Harfe g\u00f6re dizin sayfalar\u0131",
        "Indices and tables:": "Dizinler ve tablolar:",
        "Last updated on %(last_updated)s.": "Son g\u00fcncelleme: %(last_updated)s.",
        "Library changes": "K\u00fct\u00fcphane de\u011fi\u015fiklikleri",
        "Navigation": "Gezinti",
        "Next topic": "Sonraki konu",
        "Other changes": "Di\u011fer de\u011fi\u015fiklikler",
        "Overview": "Genel Bak\u0131\u015f",
        "Please activate JavaScript to enable the search\n    functionality.": "Arama i\u015flevini kullanabilmek i\u00e7in l\u00fctfen JavaScript'i\n    etkinle\u015ftirin.",
        "Preparing search...": "Aramaya haz\u0131rlan\u0131yor...",
        "Previous topic": "\u00d6nceki konu",
        "Quick search": "H\u0131zl\u0131 Arama",
        "Search": "Ara",
        "Search Page": "Arama Sayfas\u0131",
        "Search Results": "Arama Sonu\u00e7lar\u0131",
        "Search finished, found one page matching the search query.": [
            "",
            ""
        ],
        "Search within %(docstitle)s": "%(docstitle)s i\u00e7inde ara",
        "Searching": "Aran\u0131yor",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "Kayna\u011f\u0131 G\u00f6ster",
        "Table of Contents": "\u0130\u00e7indekiler",
        "This Page": "Bu Sayfa",
        "Welcome! This is": "Ho\u015f Geldiniz! Kar\u015f\u0131n\u0131zda",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "Arama sonucunda herhangi bir belge bulunamad\u0131. B\u00fct\u00fcn kelimeleri do\u011fru yazd\u0131\u011f\u0131n\u0131zdan ve gerekli b\u00fct\u00fcn kategorileri se\u00e7ti\u011finizden emin olun.",
        "all functions, classes, terms": "t\u00fcm i\u015flevler, s\u0131n\u0131flar, terimler",
        "can be huge": "\u00e7ok b\u00fcy\u00fck olabilir",
        "last updated": "son g\u00fcncelleme",
        "lists all sections and subsections": "t\u00fcm b\u00f6l\u00fcmleri ve alt b\u00f6l\u00fcmleri listeler",
        "next chapter": "sonraki b\u00f6l\u00fcm",
        "previous chapter": "\u00f6nceki b\u00f6l\u00fcm",
        "quick access to all modules": "t\u00fcm mod\u00fcllere h\u0131zl\u0131 eri\u015fim",
        "search": "ara",
        "search this documentation": "bu belgelendirmeyi ara",
        "the documentation for": "belgelendirme konusu: "
    },
    "plural_expr": "(n > 1)"
});