Documentation.addTranslations({
    "locale": "lt",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": "",
        "About these documents": "Apie \u0161iuos dokumentus",
        "Automatically generated list of changes in version %(version)s": "Automati\u0161kai sugeneruotas pakeitim\u0173 %(version)s versijoje s\u0105ra\u0161as",
        "C API changes": "C API pakeitimai",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "",
        "Collapse sidebar": "Pasl\u0117pti \u0161onin\u0119 juost\u0105",
        "Complete Table of Contents": "Pilnas Turinys",
        "Contents": "Turinys",
        "Copyright": "Autoriaus teis\u0117s",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "I\u0161pl\u0117sti \u0161onin\u0119 juost\u0105",
        "Full index on one page": "Pilnas indeksas viename puslapyje",
        "General Index": "Bendras indeksas",
        "Global Module Index": "Globalus Modulio Indeksas",
        "Go": "Pirmyn",
        "Hide Search Matches": "Pasl\u0117pti paie\u0161kos rezultatus",
        "Index": "Indeksas",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "Indekso puslapiai pagal raid\u0119",
        "Indices and tables:": "Indeksai ir lentel\u0117s:",
        "Last updated on %(last_updated)s.": "Paskutinis atnaujinimas %(last_updated)s.",
        "Library changes": "Bibliotekos pakeitimai",
        "Navigation": "Navigacija",
        "Next topic": "Kita tema",
        "Other changes": "Kiti pakeitimai",
        "Overview": "Ap\u017evalga",
        "Please activate JavaScript to enable the search\n    functionality.": "Pra\u0161ome aktyvuoti JavaScript, kad veikt\u0173 paie\u0161kos\n    funkcionalumas.",
        "Preparing search...": "",
        "Previous topic": "Praeita tema",
        "Quick search": "Greitoji paie\u0161ka",
        "Search": "Paie\u0161ka",
        "Search Page": "Paie\u0161kos puslapis",
        "Search Results": "Paie\u0161kos rezultatai",
        "Search finished, found one page matching the search query.": [
            "",
            "",
            "",
            ""
        ],
        "Search within %(docstitle)s": "Ie\u0161koti tarp %(docstitle)s",
        "Searching": "",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "Rodyti pirmin\u012f kod\u0105",
        "Table of Contents": "",
        "This Page": "\u0160is puslapis",
        "Welcome! This is": "",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "",
        "all functions, classes, terms": "visos funkcijos, klas\u0117s ir terminai",
        "can be huge": "gali b\u016bti didelis",
        "last updated": "",
        "lists all sections and subsections": "sura\u0161yti visus skyrius ir poskyrius",
        "next chapter": "kita dalis",
        "previous chapter": "praeita dalis",
        "quick access to all modules": "greitas vis\u0173 moduli\u0173 pasiekimas",
        "search": "ie\u0161koti",
        "search this documentation": "ie\u0161koti \u0161iame dokumente",
        "the documentation for": ""
    },
    "plural_expr": "(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3)"
});