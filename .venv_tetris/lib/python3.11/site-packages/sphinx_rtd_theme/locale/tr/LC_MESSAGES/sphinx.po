# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# BouRock, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-02-06 15:36+0100\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: BouRock, 2020\n"
"Language-Team: Turkish (https://www.transifex.com/readthedocs/teams/101354/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: sphinx_rtd_theme/breadcrumbs.html:37 sphinx_rtd_theme/breadcrumbs.html:39
msgid "Edit on GitHub"
msgstr "GitHub'da Düzenle"

#: sphinx_rtd_theme/breadcrumbs.html:44 sphinx_rtd_theme/breadcrumbs.html:46
msgid "Edit on Bitbucket"
msgstr "Bitbucket'ta Düzenle"

#: sphinx_rtd_theme/breadcrumbs.html:51 sphinx_rtd_theme/breadcrumbs.html:53
msgid "Edit on GitLab"
msgstr "GitLab'ta Düzenle"

#: sphinx_rtd_theme/breadcrumbs.html:56 sphinx_rtd_theme/breadcrumbs.html:58
msgid "View page source"
msgstr "Sayfa kaynağını görüntüle"

#: sphinx_rtd_theme/breadcrumbs.html:69 sphinx_rtd_theme/footer.html:6
msgid "Previous"
msgstr "Önceki"

#: sphinx_rtd_theme/breadcrumbs.html:72 sphinx_rtd_theme/footer.html:9
msgid "Next"
msgstr "Sonraki"

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:30
msgid "Build"
msgstr "Oluşturma"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:36
msgid "Revision"
msgstr "Gözden geçirme"

#: sphinx_rtd_theme/footer.html:41
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Son olarak %(last_updated)s tarihinde güncellendi."

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documentation
#: sphinx_rtd_theme/footer.html:55
msgid "theme"
msgstr "tema"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:57
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "kullanılarak %(readthedocs_web)s tarafından sağlanmasıyla oluşturuldu"

#: sphinx_rtd_theme/layout.html:97
#, python-format
msgid "Search within %(docstitle)s"
msgstr "%(docstitle)s içinde ara"

#: sphinx_rtd_theme/layout.html:105
msgid "About these documents"
msgstr "Bu belgeler hakkında"

#: sphinx_rtd_theme/layout.html:108
msgid "Index"
msgstr "Dizin"

#: sphinx_rtd_theme/layout.html:111 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Arama"

#: sphinx_rtd_theme/layout.html:114
msgid "Copyright"
msgstr "Telif hakkı"

#: sphinx_rtd_theme/layout.html:143
msgid "Logo"
msgstr "Logo"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr ""
"Arama işlevselliğini etkinleştirmek için lütfen JavaScript'i etkinleştirin."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Arama Sonuçları"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"Aramanız hiçbir belgeyle eşleşmedi. Lütfen tüm kelimelerin doğru "
"yazıldığından ve yeterli kategori seçtiğinizden emin olun."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Belgeleri arayın"

#: sphinx_rtd_theme/versions.html:3 sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Sürümler"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "İndirmeler"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "Read the Docs Üzerinde"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Proje Ana Sayfa"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "Oluşturmalar"
