Metadata-Version: 2.2
Name: autopep8
Version: 2.3.2
Summary: A tool that automatically formats Python code to conform to the PEP 8 style guide
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
Project-URL: Repository, https://github.com/hhatto/autopep8
Keywords: automation,pep8,format,pycodestyle
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Quality Assurance
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: AUTHORS.rst
Requires-Dist: pycodestyle>=2.12.0
Requires-Dist: tomli; python_version < "3.11"

========
autopep8
========

.. image:: https://img.shields.io/pypi/v/autopep8.svg
    :target: https://pypi.org/project/autopep8/
    :alt: PyPI Version

.. image:: https://github.com/hhatto/autopep8/workflows/Python%20package/badge.svg
    :target: https://github.com/hhatto/autopep8/actions
    :alt: Build status

.. image:: https://codecov.io/gh/hhatto/autopep8/branch/main/graph/badge.svg
    :target: https://codecov.io/gh/hhatto/autopep8
    :alt: Code Coverage

autopep8 automatically formats Python code to conform to the `PEP 8`_ style
guide. It uses the pycodestyle_ utility to determine what parts of the code
needs to be formatted. autopep8 is capable of fixing most of the formatting
issues_ that can be reported by pycodestyle.

.. _PEP 8: https://www.python.org/dev/peps/pep-0008/
.. _issues: https://pycodestyle.readthedocs.org/en/latest/intro.html#error-codes

.. contents::


Installation
============

From pip::

    $ pip install --upgrade autopep8

Consider using the ``--user`` option_.

.. _option: https://pip.pypa.io/en/latest/user_guide/#user-installs


Requirements
============

autopep8 requires pycodestyle_.

.. _pycodestyle: https://github.com/PyCQA/pycodestyle


Usage
=====

To modify a file in place (with aggressive level 2)::

    $ autopep8 --in-place --aggressive --aggressive <filename>

Before running autopep8.

.. code-block:: python

    import math, sys;

    def example1():
        ####This is a long comment. This should be wrapped to fit within 72 characters.
        some_tuple=(   1,2, 3,'a'  );
        some_variable={'long':'Long code lines should be wrapped within 79 characters.',
        'other':[math.pi, 100,200,300,9876543210,'This is a long string that goes on'],
        'more':{'inner':'This whole logical line should be wrapped.',some_tuple:[1,
        20,300,40000,500000000,60000000000000000]}}
        return (some_tuple, some_variable)
    def example2(): return {'has_key() is deprecated':True}.has_key({'f':2}.has_key(''));
    class Example3(   object ):
        def __init__    ( self, bar ):
         #Comments should have a space after the hash.
         if bar : bar+=1;  bar=bar* bar   ; return bar
         else:
                        some_string = """
    		           Indentation in multiline strings should not be touched.
    Only actual code should be reindented.
    """
                        return (sys.path, some_string)

After running autopep8.

.. code-block:: python

    import math
    import sys


    def example1():
        # This is a long comment. This should be wrapped to fit within 72
        # characters.
        some_tuple = (1, 2, 3, 'a')
        some_variable = {
            'long': 'Long code lines should be wrapped within 79 characters.',
            'other': [
                math.pi,
                100,
                200,
                300,
                9876543210,
                'This is a long string that goes on'],
            'more': {
                'inner': 'This whole logical line should be wrapped.',
                some_tuple: [
                    1,
                    20,
                    300,
                    40000,
                    500000000,
                    60000000000000000]}}
        return (some_tuple, some_variable)


    def example2(): return ('' in {'f': 2}) in {'has_key() is deprecated': True}


    class Example3(object):
        def __init__(self, bar):
            # Comments should have a space after the hash.
            if bar:
                bar += 1
                bar = bar * bar
                return bar
            else:
                some_string = """
    		           Indentation in multiline strings should not be touched.
    Only actual code should be reindented.
    """
                return (sys.path, some_string)

Options::

    usage: autopep8 [-h] [--version] [-v] [-d] [-i] [--global-config filename]
                    [--ignore-local-config] [-r] [-j n] [-p n] [-a]
                    [--experimental] [--exclude globs] [--list-fixes]
                    [--ignore errors] [--select errors] [--max-line-length n]
                    [--line-range line line] [--hang-closing] [--exit-code]
                    [files [files ...]]

    Automatically formats Python code to conform to the PEP 8 style guide.

    positional arguments:
      files                 files to format or '-' for standard in

    optional arguments:
      -h, --help            show this help message and exit
      --version             show program's version number and exit
      -v, --verbose         print verbose messages; multiple -v result in more
                            verbose messages
      -d, --diff            print the diff for the fixed source
      -i, --in-place        make changes to files in place
      --global-config filename
                            path to a global pep8 config file; if this file does
                            not exist then this is ignored (default:
                            ~/.config/pep8)
      --ignore-local-config
                            don't look for and apply local config files; if not
                            passed, defaults are updated with any config files in
                            the project's root directory
      -r, --recursive       run recursively over directories; must be used with
                            --in-place or --diff
      -j n, --jobs n        number of parallel jobs; match CPU count if value is
                            less than 1
      -p n, --pep8-passes n
                            maximum number of additional pep8 passes (default:
                            infinite)
      -a, --aggressive      enable non-whitespace changes; multiple -a result in
                            more aggressive changes
      --experimental        enable experimental fixes
      --exclude globs       exclude file/directory names that match these comma-
                            separated globs
      --list-fixes          list codes for fixes; used by --ignore and --select
      --ignore errors       do not fix these errors/warnings (default:
                            E226,E24,W50,W690)
      --select errors       fix only these errors/warnings (e.g. E4,W)
      --max-line-length n   set maximum allowed line length (default: 79)
      --line-range line line, --range line line
                            only fix errors found within this inclusive range of
                            line numbers (e.g. 1 99); line numbers are indexed at
                            1
      --hang-closing        hang-closing option passed to pycodestyle
      --exit-code           change to behavior of exit code. default behavior of
                            return value, 0 is no differences, 1 is error exit.
                            return 2 when add this option. 2 is exists
                            differences.


Features
========

autopep8 fixes the following issues_ reported by pycodestyle_::

    E101 - Reindent all lines.
    E11  - Fix indentation.
    E121 - Fix indentation to be a multiple of four.
    E122 - Add absent indentation for hanging indentation.
    E123 - Align closing bracket to match opening bracket.
    E124 - Align closing bracket to match visual indentation.
    E125 - Indent to distinguish line from next logical line.
    E126 - Fix over-indented hanging indentation.
    E127 - Fix visual indentation.
    E128 - Fix visual indentation.
    E129 - Fix visual indentation.
    E131 - Fix hanging indent for unaligned continuation line.
    E133 - Fix missing indentation for closing bracket.
    E20  - Remove extraneous whitespace.
    E211 - Remove extraneous whitespace.
    E22  - Fix extraneous whitespace around keywords.
    E224 - Remove extraneous whitespace around operator.
    E225 - Fix missing whitespace around operator.
    E226 - Fix missing whitespace around arithmetic operator.
    E227 - Fix missing whitespace around bitwise/shift operator.
    E228 - Fix missing whitespace around modulo operator.
    E231 - Add missing whitespace.
    E241 - Fix extraneous whitespace around keywords.
    E242 - Remove extraneous whitespace around operator.
    E251 - Remove whitespace around parameter '=' sign.
    E252 - Missing whitespace around parameter equals.
    E26  - Fix spacing after comment hash for inline comments.
    E265 - Fix spacing after comment hash for block comments.
    E266 - Fix too many leading '#' for block comments.
    E27  - Fix extraneous whitespace around keywords.
    E301 - Add missing blank line.
    E302 - Add missing 2 blank lines.
    E303 - Remove extra blank lines.
    E304 - Remove blank line following function decorator.
    E305 - Expected 2 blank lines after end of function or class.
    E306 - Expected 1 blank line before a nested definition.
    E401 - Put imports on separate lines.
    E402 - Fix module level import not at top of file
    E501 - Try to make lines fit within --max-line-length characters.
    E502 - Remove extraneous escape of newline.
    E701 - Put colon-separated compound statement on separate lines.
    E70  - Put semicolon-separated compound statement on separate lines.
    E711 - Fix comparison with None.
    E712 - Fix comparison with boolean.
    E713 - Use 'not in' for test for membership.
    E714 - Use 'is not' test for object identity.
    E721 - Use "isinstance()" instead of comparing types directly.
    E722 - Fix bare except.
    E731 - Use a def when use do not assign a lambda expression.
    W291 - Remove trailing whitespace.
    W292 - Add a single newline at the end of the file.
    W293 - Remove trailing whitespace on blank line.
    W391 - Remove trailing blank lines.
    W503 - Fix line break before binary operator.
    W504 - Fix line break after binary operator.
    W605 - Fix invalid escape sequence 'x'.

autopep8 also fixes some issues not found by pycodestyle_.

- Normalize files with mixed line endings.
- Put a blank line between a class docstring and its first method
  declaration. (Enabled with ``E301``.)
- Remove blank lines between a function declaration and its docstring. (Enabled
  with ``E303``.)

autopep8 avoids fixing some issues found by pycodestyle_.

- ``E112``/``E113`` for non comments are reports of bad indentation that break
  syntax rules. These should not be modified at all.
- ``E265``, which refers to spacing after comment hash, is ignored if the
  comment looks like code. autopep8 avoids modifying these since they are not
  real comments. If you really want to get rid of the pycodestyle_ warning,
  consider just removing the commented-out code. (This can be automated via
  eradicate_.)

.. _eradicate: https://github.com/myint/eradicate


More advanced usage
===================

By default autopep8 only makes whitespace changes. Thus, by default, it does
not fix ``E711`` and ``E712``. (Changing ``x == None`` to ``x is None`` may
change the meaning of the program if ``x`` has its ``__eq__`` method
overridden.) Nor does it correct deprecated code ``W6``. To enable these
more aggressive fixes, use the ``--aggressive`` option::

    $ autopep8 --aggressive <filename>

Use multiple ``--aggressive`` to increase the aggressiveness level. For
example, ``E712`` requires aggressiveness level 2 (since ``x == True`` could be
changed to either ``x`` or ``x is True``, but autopep8 chooses the former).

``--aggressive`` will also shorten lines more aggressively. It will also remove
trailing whitespace more aggressively. (Usually, we don't touch trailing
whitespace in docstrings and other multiline strings. And to do even more
aggressive changes to docstrings, use docformatter_.)

.. _docformatter: https://github.com/myint/docformatter

To enable only a subset of the fixes, use the ``--select`` option. For example,
to fix various types of indentation issues::

    $ autopep8 --select=E1,W1 <filename>

If the file being fixed is large, you may want to enable verbose progress
messages::

    $ autopep8 -v <filename>

Passing in ``--experimental`` enables the following functionality:

- Shortens code lines by taking its length into account

::

$ autopep8 --experimental <filename>

Disabling line-by-line
----------------------

It is possible to disable autopep8 until it is turned back on again in the file,
using ``autopep8: off`` and then reenabling with ``autopep8: on``.

.. code-block:: python

    # autopep8: off
        [
            [23, 23, 13, 43],
            [32, 34, 34, 34],
            [56, 34, 34, 11],
            [10, 10, 10, 10],
        ]
    # autopep8: on
         
``fmt: off`` and ``fmt: on`` are also valid.

Use as a module
===============

The simplest way of using autopep8 as a module is via the ``fix_code()``
function:

    >>> import autopep8
    >>> autopep8.fix_code('x=       123\n')
    'x = 123\n'

Or with options:

    >>> import autopep8
    >>> autopep8.fix_code('print( 123 )\n',
    ...                   options={'ignore': ['E']})
    'print( 123 )\n'


Configuration
=============

By default, if ``$HOME/.config/pycodestyle`` (``~\.pycodestyle`` in Windows
environment) exists, it will be used as global configuration file.
Alternatively, you can specify the global configuration file with the
``--global-config`` option.

Also, if ``setup.cfg``, ``tox.ini``, ``.pep8`` and ``.flake8`` files exist
in the directory where the target file exists, it will be used as the
configuration file.

``pep8``, ``pycodestyle``, and ``flake8`` can be used as a section.

configuration file example::

    [pycodestyle]
    max_line_length = 120
    ignore = E501

pyproject.toml
--------------

autopep8 can also use ``pyproject.toml``.
The section must be ``[tool.autopep8]``, and ``pyproject.toml`` takes precedence
over any other configuration files.

configuration file example::

    [tool.autopep8]
    max_line_length = 120
    ignore = "E501,W6"  # or ["E501", "W6"]
    in-place = true
    recursive = true
    aggressive = 3

Usage with pre-commit
=====================

autopep8 can be used as a hook for pre-commit_.

To add autopep8 as a plugin, add this repo definition to your configuration:

.. code-block:: yaml

    repos:
    -   repo: https://github.com/hhatto/autopep8
        rev: ...  # select the tag or revision you want, or run `pre-commit autoupdate`
        hooks:
        -   id: autopep8

.. _`pre-commit`: https://pre-commit.com


Testing
=======

Test cases are in ``test/test_autopep8.py``. They can be run directly via
``python test/test_autopep8.py`` or via tox_. The latter is useful for
testing against multiple Python interpreters. (We currently test against
CPython versions 3.9, 3.10, 3.11, 3.12 and 3.13. We also test against PyPy.)

.. _`tox`: https://pypi.org/project/tox/

Broad spectrum testing is available via ``test/acid.py``. This script runs
autopep8 against Python code and checks for correctness and completeness of the
code fixes. It can check that the bytecode remains identical.
``test/acid_pypi.py`` makes use of ``acid.py`` to test against the latest
released packages on PyPI.


Troubleshooting
===============

``pkg_resources.DistributionNotFound``
--------------------------------------

If you are using an ancient version of ``setuptools``, you might encounter
``pkg_resources.DistributionNotFound`` when trying to run ``autopep8``. Try
upgrading ``setuptools`` to workaround this ``setuptools`` problem::

    $ pip install --upgrade setuptools

Use ``sudo`` if you are installing to the system.


Links
=====

* PyPI_
* GitHub_
* Codecov_

.. _PyPI: https://pypi.org/project/autopep8/
.. _GitHub: https://github.com/hhatto/autopep8
.. _`Codecov`: https://app.codecov.io/gh/hhatto/autopep8
