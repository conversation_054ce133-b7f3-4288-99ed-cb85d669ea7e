{"data_mtime": 1749977453, "dep_lines": [11, 12, 19, 20, 21, 22, 23, 24, 1, 2, 3, 9, 10, 13, 14, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "concurrent.futures", "asyncio.base_events", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "asyncio.transports", "asyncio.unix_events", "ssl", "sys", "_asyncio", "_typeshed", "abc", "<PERSON><PERSON><PERSON>", "socket", "typing", "typing_extensions", "asyncio", "builtins", "_contextvars", "_frozen_importlib", "_socket", "_ssl", "concurrent", "concurrent.futures._base", "enum", "os", "types"], "hash": "1d96335b753fa6cda1e8e6d100dbad2f661f5724", "id": "asyncio.events", "ignore_all": true, "interface_hash": "781280995c4d32c0839bf093786bb311ca54caeb", "mtime": 1750035294, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/mypy/typeshed/stdlib/asyncio/events.pyi", "plugin_data": null, "size": 24609, "suppressed": [], "version_id": "1.16.0"}