{"data_mtime": **********, "dep_lines": [21, 8, 9, 10, 11, 12, 23, 24, 25, 30, 31, 33, 34, 36, 42, 43, 47, 52, 57, 62, 63, 66, 69, 71, 72, 74, 76, 77, 4, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest._code", "_pytest.assertion", "_pytest.cacheprovider", "_pytest.capture", "_pytest.config", "_pytest.debugging", "_pytest.doctest", "_pytest.fixtures", "_pytest.freeze_support", "_pytest.legacypath", "_pytest.logging", "_pytest.main", "_pytest.mark", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.outcomes", "_pytest.pytester", "_pytest.python", "_pytest.python_api", "_pytest.raises", "_pytest.recwarn", "_pytest.reports", "_pytest.runner", "_pytest.stash", "_pytest.terminal", "_pytest.tmpdir", "_pytest.warning_types", "__future__", "_pytest", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "e75824a5efb2af29cd24c2d68f9d77e564602175", "id": "pytest", "ignore_all": true, "interface_hash": "c71d32f7652ea2d838359a36f99f22fbe15d9d2f", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/pytest/__init__.py", "plugin_data": null, "size": 5278, "suppressed": [], "version_id": "1.16.0"}