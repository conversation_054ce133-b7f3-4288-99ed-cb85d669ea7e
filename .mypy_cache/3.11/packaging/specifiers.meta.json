{"data_mtime": 1749977453, "dep_lines": [18, 19, 11, 13, 14, 15, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["packaging.utils", "packaging.version", "__future__", "abc", "itertools", "re", "typing", "builtins", "_frozen_importlib", "enum", "types", "typing_extensions"], "hash": "469aec1a70316d38f8a60a70ae8189327a066c37", "id": "packaging.specifiers", "ignore_all": true, "interface_hash": "1d0b828302ce70a3a06856f14f5c484e92d7066e", "mtime": 1750035292, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/packaging/specifiers.py", "plugin_data": null, "size": 40055, "suppressed": [], "version_id": "1.16.0"}