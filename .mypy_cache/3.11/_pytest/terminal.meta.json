{"data_mtime": 1749977454, "dep_lines": [37, 39, 41, 46, 11, 34, 35, 36, 38, 40, 42, 49, 51, 57, 567, 7, 9, 10, 15, 16, 17, 18, 19, 20, 21, 22, 23, 30, 32, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 5, 5, 25, 20, 5, 10, 5, 10, 10, 5, 10, 5, 10, 10, 10, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io.wcwidth", "_pytest.assertion.util", "_pytest.config.argparsing", "collections.abc", "_pytest.nodes", "_pytest.timing", "_pytest._code", "_pytest._io", "_pytest._version", "_pytest.config", "_pytest.pathlib", "_pytest.reports", "_pytest.main", "_pytest.warnings", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "collections", "dataclasses", "datetime", "functools", "inspect", "pathlib", "platform", "sys", "textwrap", "typing", "warnings", "pluggy", "_pytest", "builtins", "_collections_abc", "_frozen_importlib", "_pytest._io.terminalwriter", "abc", "enum", "os", "pluggy._hooks", "pluggy._manager", "types", "typing_extensions"], "hash": "45e6f413eae76a41d90d92db3ea00abcf5345b63", "id": "_pytest.terminal", "ignore_all": true, "interface_hash": "ea5fc8eab99683cfbc875162f819aed919c3c02f", "mtime": 1750035293, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/terminal.py", "plugin_data": null, "size": 60168, "suppressed": [], "version_id": "1.16.0"}