{"data_mtime": 1749977454, "dep_lines": [27, 34, 36, 5, 25, 31, 32, 35, 39, 40, 41, 42, 49, 324, 413, 2, 4, 9, 11, 12, 13, 15, 21, 23, 25, 46, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 10, 5, 5, 10, 5, 5, 10, 10, 20, 25, 5, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.compat", "_pytest.mark.structures", "collections.abc", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.main", "_pytest.mark", "_pytest.fixtures", "__future__", "abc", "functools", "inspect", "os", "pathlib", "typing", "warnings", "pluggy", "_pytest", "typing_extensions", "builtins", "_frozen_importlib", "pluggy._hooks", "types"], "hash": "196e25a3780a24a3c6671ad25562d39c84577cde", "id": "_pytest.nodes", "ignore_all": true, "interface_hash": "8808ee67694835cff558ce048c743a07b827a247", "mtime": 1750035293, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/nodes.py", "plugin_data": null, "size": 26533, "suppressed": [], "version_id": "1.16.0"}