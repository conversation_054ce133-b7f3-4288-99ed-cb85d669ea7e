{"data_mtime": 1749977454, "dep_lines": [6, 24, 25, 26, 4, 9, 10, 11, 12, 20, 22, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 25, 10, 5, 30, 30, 30], "dependencies": ["collections.abc", "_pytest.deprecated", "_pytest.fixtures", "_pytest.outcomes", "__future__", "pprint", "re", "types", "typing", "typing_extensions", "warnings", "builtins", "_frozen_importlib", "_pytest.config", "abc"], "hash": "4da25af2c8ac21dadfeda307eef9947b7ea28c8a", "id": "_pytest.recwarn", "ignore_all": true, "interface_hash": "52d363db6d1febd612bff046c747e73c47195fba", "mtime": 1750035293, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/recwarn.py", "plugin_data": null, "size": 13245, "suppressed": [], "version_id": "1.16.0"}