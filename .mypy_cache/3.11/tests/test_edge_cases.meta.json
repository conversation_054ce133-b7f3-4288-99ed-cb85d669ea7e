{"data_mtime": 1750015305, "dep_lines": [13, 14, 15, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tetris_engine.constants", "tetris_engine.core", "tetris_engine.io", "random", "time", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.raises", "_typeshed", "abc", "enum", "os", "re", "tetris_engine", "typing", "typing_extensions"], "hash": "f87601b4d8701e2ba0ef04fe3dafd441351530d6", "id": "tests.test_edge_cases", "ignore_all": false, "interface_hash": "2a952ca291d1b5fd44137c5bc736fcd0acce066d", "mtime": 1750038432, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "tests/test_edge_cases.py", "plugin_data": null, "size": 13137, "suppressed": [], "version_id": "1.16.0"}