{".class": "MypyFile", "_fullname": "tests.test_multi_drops", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Board": {".class": "SymbolTableNode", "cross_ref": "tetris_engine.core.Board", "kind": "Gdef"}, "SINGLE_PIECE_HEIGHTS": {".class": "SymbolTableNode", "cross_ref": "tetris_engine.constants.SINGLE_PIECE_HEIGHTS", "kind": "Gdef"}, "TETROMINO": {".class": "SymbolTableNode", "cross_ref": "tetris_engine.constants.TETROMINO", "kind": "Gdef"}, "TetrisEngineUnitTestMultiDropRunner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner", "name": "TetrisEngineUnitTestMultiDropRunner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_multi_drops", "mro": ["tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner.__init__", "name": "__init__", "type": null}}, "_compute_valid_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner._compute_valid_positions", "name": "_compute_valid_positions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_valid_positions of TetrisEngineUnitTestMultiDropRunner", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tetromino_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner.tetromino_types", "name": "tetromino_types", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "valid_positions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner.valid_positions", "name": "valid_positions", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WIDTH": {".class": "SymbolTableNode", "cross_ref": "tetris_engine.constants.WIDTH", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_multi_drops.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_multi_drops.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_multi_drops.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_multi_drops.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_multi_drops.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_multi_drops.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "process_file": {".class": "SymbolTableNode", "cross_ref": "tetris_engine.io.process_file", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "runner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tests.test_multi_drops.runner", "name": "runner", "setter_type": null, "type": "tests.test_multi_drops.TetrisEngineUnitTestMultiDropRunner"}}, "test_boundary_conditions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_multi_drops.test_boundary_conditions", "name": "test_boundary_conditions", "type": null}}, "test_io_basic_patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_multi_drops.test_io_basic_patterns", "name": "test_io_basic_patterns", "type": null}}, "test_line_clearing_patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_multi_drops.test_line_clearing_patterns", "name": "test_line_clearing_patterns", "type": null}}, "test_single_piece_all_positions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_multi_drops.test_single_piece_all_positions", "name": "test_single_piece_all_positions", "type": null}}, "test_stacking_all_combinations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_multi_drops.test_stacking_all_combinations", "name": "test_stacking_all_combinations", "type": null}}}, "path": "tests/test_multi_drops.py"}