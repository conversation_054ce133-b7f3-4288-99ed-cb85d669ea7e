["tests/test_core.py::test_full_board_clear", "tests/test_core.py::test_multi_row_clear_then_drop", "tests/test_core.py::test_no_clear_sequence", "tests/test_core.py::test_occupied_columns[I-3-cols0]", "tests/test_core.py::test_occupied_columns[Q-4-cols2]", "tests/test_core.py::test_occupied_columns[T-1-cols1]", "tests/test_core.py::test_right_boundary[I-6]", "tests/test_core.py::test_right_boundary[J-8]", "tests/test_core.py::test_right_boundary[L-8]", "tests/test_core.py::test_right_boundary[Q-8]", "tests/test_core.py::test_right_boundary[S-7]", "tests/test_core.py::test_right_boundary[T-7]", "tests/test_core.py::test_right_boundary[Z-7]", "tests/test_core.py::test_single_piece_drop[I-0-1]", "tests/test_core.py::test_single_piece_drop[J-0-3]", "tests/test_core.py::test_single_piece_drop[L-0-3]", "tests/test_core.py::test_single_piece_drop[Q-0-2]", "tests/test_core.py::test_single_piece_drop[S-0-2]", "tests/test_core.py::test_single_piece_drop[T-0-2]", "tests/test_core.py::test_single_piece_drop[Z-0-2]", "tests/test_core.py::test_stack_two_drops", "tests/test_edge_cases.py::TestConsistencyAndInvariants::test_column_heights_consistency", "tests/test_edge_cases.py::TestConsistencyAndInvariants::test_height_consistency", "tests/test_edge_cases.py::TestConsistencyAndInvariants::test_occupied_cells_consistency", "tests/test_edge_cases.py::TestConsistencyAndInvariants::test_row_count_consistency", "tests/test_edge_cases.py::TestEdgeCases::test_alternating_columns", "tests/test_edge_cases.py::TestEdgeCases::test_complex_line_clearing", "tests/test_edge_cases.py::TestEdgeCases::test_empty_board_properties", "tests/test_edge_cases.py::TestEdgeCases::test_extreme_stacking", "tests/test_edge_cases.py::TestEdgeCases::test_invalid_piece_types", "tests/test_edge_cases.py::TestEdgeCases::test_invalid_positions_negative", "tests/test_edge_cases.py::TestEdgeCases::test_invalid_positions_too_large", "tests/test_edge_cases.py::TestEdgeCases::test_maximum_valid_positions", "tests/test_edge_cases.py::TestEdgeCases::test_minimum_valid_positions", "tests/test_edge_cases.py::TestEdgeCases::test_partial_overlap_stacking", "tests/test_edge_cases.py::TestEdgeCases::test_simultaneous_multi_row_clear", "tests/test_edge_cases.py::TestStressConditions::test_io_stress_large_file", "tests/test_edge_cases.py::TestStressConditions::test_large_sequence_performance", "tests/test_edge_cases.py::TestStressConditions::test_memory_usage_large_board", "tests/test_edge_cases.py::TestStressConditions::test_random_sequence_stability", "tests/test_edge_cases.py::TestStressConditions::test_worst_case_no_clears", "tests/test_exhaustive.py::test_boundary_conditions", "tests/test_exhaustive.py::test_io_exhaustive_patterns", "tests/test_exhaustive.py::test_line_clearing_patterns", "tests/test_exhaustive.py::test_memory_and_performance", "tests/test_exhaustive.py::test_sequence_sampling", "tests/test_exhaustive.py::test_single_piece_all_positions", "tests/test_exhaustive.py::test_stacking_all_combinations", "tests/test_io.py::test_process_file", "tests/test_io.py::test_process_file_empty", "tests/test_io.py::test_process_file_stdout", "tests/test_multi_drops.py::test_boundary_conditions", "tests/test_multi_drops.py::test_io_basic_patterns", "tests/test_multi_drops.py::test_line_clearing_patterns", "tests/test_multi_drops.py::test_single_piece_all_positions", "tests/test_multi_drops.py::test_stacking_all_combinations"]